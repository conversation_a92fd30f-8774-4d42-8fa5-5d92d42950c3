{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1755673030427}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoBA;AAEA;EACAA,wBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAG;IAFA,CALA;IASAC;MACAJ,YADA;MAEAG;IAFA;EATA,CAFA;;EAgBAE;IACA;MACAC,eADA;MAEAC;IAFA;EAIA,CArBA;;EAsBAC;IACA;EACA,CAxBA;;EAyBAC;IACA;MACA;IACA;;IACA;MACA;IACA;EACA,CAhCA;;EAiCAC;IACAC;MACA;MACA;IACA,CAJA;;IAMAC;MACA;MACA;MAEA,8CAJA,CAMA;;MACA;MACA;MACA;MACA,mBAVA,CAYA;;MACA;QACA;QACAC;UACAC,QADA;UACA;UACAC;YACAC,gEADA;YAEAC;UAFA;QAFA,GAFA,CASA;;QACAC;UACAJ,QADA;UACA;UACAC;YACAC,oBADA;YAEAC;UAFA;QAFA;MAOA,CA9BA,CAgCA;;;MACA;;MACA;QACAE;QACAA;MACA;;MAEA;QACAC,eADA;QAEAC,uBAFA;QAGAC,2BAHA;QAIAC,SACA;QACA;UACAvB,WADA;UAEAwB,qBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,+BADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CAFA,EAiBA;QACA;UACA7B,WADA;UAEAwB,sBAFA;UAGAC,sBAHA;UAIAK,cAJA;UAKAzB,kBALA;UAMAqB;YAAAC;UAAA,CANA;UAOAC;YAAAD;UAAA,CAPA;UAQAE;QARA,CAlBA,EA4BA;QACA;UACA7B,WADA;UAEAwB,uBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,gBADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CA7BA;MAJA;MAmDA;IACA,CAjGA;;IAmGAE;MACA;MACA;MAEA,gDAJA,CAMA;;MACA;MACA;MACA;MACA,mBAVA,CAYA;;MACA;QACA;QACAlB;UACAC,QADA;UACA;UACAC;YACAC,gEADA;YAEAC;UAFA;QAFA,GAFA,CASA;;QACAC;UACAJ,QADA;UACA;UACAC;YACAC,oBADA;YAEAC;UAFA;QAFA;MAOA,CA9BA,CAgCA;;;MACA;;MACA;QACAE;QACAA;MACA;;MAEA;QACAC,eADA;QAEAC,uBAFA;QAGAC,2BAHA;QAIAC,SACA;QACA;UACAvB,WADA;UAEAwB,qBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,+BADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CAFA,EAiBA;QACA;UACA7B,WADA;UAEAwB,sBAFA;UAGAC,sBAHA;UAIAK,cAJA;UAKAzB,kBALA;UAMAqB;YAAAC;UAAA,CANA;UAOAC;YAAAD;UAAA,CAPA;UAQAE;QARA,CAlBA,EA4BA;QACA;UACA7B,WADA;UAEAwB,uBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,gBADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CA7BA;MAJA;MAmDA;IACA;;EA9LA;AAjCA", "names": ["name", "props", "id", "type", "required", "maleRatio", "default", "femaleRatio", "data", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initMaleChart", "tickData", "value", "itemStyle", "color", "borderWidth", "gapData", "combinedData", "animation", "animationDuration", "animationEasing", "series", "radius", "center", "label", "show", "labelLine", "silent", "startAngle", "initFemaleChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["GenderRatioChart.vue"], "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 70\n    },\n    femaleRatio: {\n      type: Number,\n      default: 30\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.maleChart = echarts.init(chartContainer)\n\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.maleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#00D4FF' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(0, 212, 255, 0.1)',\n                borderWidth: 1\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.femaleChart = echarts.init(chartContainer)\n\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.femaleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#FFD700' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(255, 215, 0, 0.1)',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n\n  .ratio-item {\n    position: relative;\n    width: 182px;\n    height: 172px;\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 100;\n      pointer-events: none;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}