{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1755662142482}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoBA;AAEA;EACAA,wBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAG;IAFA,CALA;IASAC;MACAJ,YADA;MAEAG;IAFA;EATA,CAFA;;EAgBAE;IACA;MACAC,eADA;MAEAC;IAFA;EAIA,CArBA;;EAsBAC;IACA;EACA,CAxBA;;EAyBAC;IACA;MACA;IACA;;IACA;MACA;IACA;EACA,CAhCA;;EAiCAC;IACAC;MACA;MACA;IACA,CAJA;;IAMAC;MACA;MACA;MAEA;MAEA;QACAC,eADA;QAEAC,uBAFA;QAGAC,2BAHA;QAIAC,SACA;UACAhB,WADA;UAEAiB,sBAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAd,OACA;YACAe,qBADA;YAEAC;cACAC,uDACA;gBAAAC;gBAAAD;cAAA,CADA,EAEA;gBAAAC;gBAAAD;cAAA,CAFA,EAGA;gBAAAC;gBAAAD;cAAA,CAHA,EADA;cAMAE,cANA;cAOAC;YAPA;UAFA,CADA,EAaA;YACAL,2BADA;YAEAC;cACAC;YADA;UAFA,CAbA,CALA;UAyBAI;YACAC;UADA,CAzBA;UA4BAC;YACAD;UADA,CA5BA;UA+BAE;QA/BA,CADA,EAkCA;QACA;UACA7B,WADA;UAEAiB,sBAFA;UAGAC,sBAHA;UAIAb,OACA;YACAe,UADA;YAEAC;cACAC;YADA;UAFA,CADA,CAJA;UAYAI;YACAC;UADA,CAZA;UAeAE;QAfA,CAnCA;MAJA;MA2DA;IACA,CAxEA;;IA0EAC;MACA;MACA;MAEA;MAEA;QACAjB,eADA;QAEAC,uBAFA;QAGAC,2BAHA;QAIAC,SACA;UACAhB,WADA;UAEAiB,sBAFA;UAGAC,sBAHA;UAIAC,cAJA;UAKAd,OACA;YACAe,uBADA;YAEAC;cACAC,uDACA;gBAAAC;gBAAAD;cAAA,CADA,EAEA;gBAAAC;gBAAAD;cAAA,CAFA,EAGA;gBAAAC;gBAAAD;cAAA,CAHA,EADA;cAMAE,cANA;cAOAC;YAPA;UAFA,CADA,EAaA;YACAL,6BADA;YAEAC;cACAC;YADA;UAFA,CAbA,CALA;UAyBAI;YACAC;UADA,CAzBA;UA4BAC;YACAD;UADA,CA5BA;UA+BAE;QA/BA,CADA,EAkCA;QACA;UACA7B,WADA;UAEAiB,sBAFA;UAGAC,sBAHA;UAIAb,OACA;YACAe,UADA;YAEAC;cACAC;YADA;UAFA,CADA,CAJA;UAYAI;YACAC;UADA,CAZA;UAeAE;QAfA,CAnCA;MAJA;MA2DA;IACA;;EA5IA;AAjCA", "names": ["name", "props", "id", "type", "required", "maleRatio", "default", "femaleRatio", "data", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initMaleChart", "animation", "animationDuration", "animationEasing", "series", "radius", "center", "startAngle", "value", "itemStyle", "color", "offset", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "label", "show", "labelLine", "silent", "initFemaleChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["GenderRatioChart.vue"], "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 70\n    },\n    femaleRatio: {\n      type: Number,\n      default: 30\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.maleChart = echarts.init(chartContainer)\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          {\n            type: 'pie',\n            radius: ['70%', '85%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: [\n              {\n                value: this.maleRatio,\n                itemStyle: {\n                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [\n                    { offset: 0, color: '#00D4FF' },\n                    { offset: 0.5, color: '#26C6DA' },\n                    { offset: 1, color: '#4FC3F7' }\n                  ]),\n                  shadowBlur: 10,\n                  shadowColor: 'rgba(0, 212, 255, 0.5)'\n                }\n              },\n              {\n                value: 100 - this.maleRatio,\n                itemStyle: {\n                  color: 'rgba(255, 255, 255, 0.08)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            labelLine: {\n              show: false\n            },\n            silent: true\n          },\n          // 外圈装饰\n          {\n            type: 'pie',\n            radius: ['88%', '90%'],\n            center: ['50%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: 'rgba(0, 212, 255, 0.3)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            silent: true\n          }\n        ]\n      }\n\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.femaleChart = echarts.init(chartContainer)\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          {\n            type: 'pie',\n            radius: ['70%', '85%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: [\n              {\n                value: this.femaleRatio,\n                itemStyle: {\n                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [\n                    { offset: 0, color: '#FFD700' },\n                    { offset: 0.5, color: '#FFCA28' },\n                    { offset: 1, color: '#FFA726' }\n                  ]),\n                  shadowBlur: 10,\n                  shadowColor: 'rgba(255, 215, 0, 0.5)'\n                }\n              },\n              {\n                value: 100 - this.femaleRatio,\n                itemStyle: {\n                  color: 'rgba(255, 255, 255, 0.08)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            labelLine: {\n              show: false\n            },\n            silent: true\n          },\n          // 外圈装饰\n          {\n            type: 'pie',\n            radius: ['88%', '90%'],\n            center: ['50%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: 'rgba(255, 215, 0, 0.3)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            silent: true\n          }\n        ]\n      }\n\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n  padding: 20px;\n\n  .ratio-item {\n    position: relative;\n    width: 140px;\n    height: 140px;\n    border: 2px dashed rgba(0, 212, 255, 0.6);\n    border-radius: 12px;\n    padding: 15px;\n    background: rgba(0, 212, 255, 0.05);\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: rgba(0, 212, 255, 0.8);\n      background: rgba(0, 212, 255, 0.1);\n      transform: scale(1.05);\n    }\n\n    &:nth-child(2) {\n      border-color: rgba(255, 215, 0, 0.6);\n      background: rgba(255, 215, 0, 0.05);\n\n      &:hover {\n        border-color: rgba(255, 215, 0, 0.8);\n        background: rgba(255, 215, 0, 0.1);\n      }\n    }\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 10;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}