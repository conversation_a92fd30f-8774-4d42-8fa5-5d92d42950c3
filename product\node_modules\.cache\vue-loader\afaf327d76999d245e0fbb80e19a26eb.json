{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1755673030427}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GenderRatioChart.vue"], "names": [], "mappings": ";AAoBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "GenderRatioChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 70\n    },\n    femaleRatio: {\n      type: Number,\n      default: 30\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.maleChart = echarts.init(chartContainer)\n\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.maleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#00D4FF' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(0, 212, 255, 0.1)',\n                borderWidth: 1\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.femaleChart = echarts.init(chartContainer)\n\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.femaleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#FFD700' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(255, 215, 0, 0.1)',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n\n  .ratio-item {\n    position: relative;\n    width: 182px;\n    height: 172px;\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 100;\n      pointer-events: none;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}