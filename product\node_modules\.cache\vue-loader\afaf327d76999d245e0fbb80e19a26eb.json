{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1755662142482}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GenderRatioChart.vue"], "names": [], "mappings": ";AAoBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "GenderRatioChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 70\n    },\n    femaleRatio: {\n      type: Number,\n      default: 30\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.maleChart = echarts.init(chartContainer)\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          {\n            type: 'pie',\n            radius: ['70%', '85%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: [\n              {\n                value: this.maleRatio,\n                itemStyle: {\n                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [\n                    { offset: 0, color: '#00D4FF' },\n                    { offset: 0.5, color: '#26C6DA' },\n                    { offset: 1, color: '#4FC3F7' }\n                  ]),\n                  shadowBlur: 10,\n                  shadowColor: 'rgba(0, 212, 255, 0.5)'\n                }\n              },\n              {\n                value: 100 - this.maleRatio,\n                itemStyle: {\n                  color: 'rgba(255, 255, 255, 0.08)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            labelLine: {\n              show: false\n            },\n            silent: true\n          },\n          // 外圈装饰\n          {\n            type: 'pie',\n            radius: ['88%', '90%'],\n            center: ['50%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: 'rgba(0, 212, 255, 0.3)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            silent: true\n          }\n        ]\n      }\n\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.femaleChart = echarts.init(chartContainer)\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          {\n            type: 'pie',\n            radius: ['70%', '85%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: [\n              {\n                value: this.femaleRatio,\n                itemStyle: {\n                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [\n                    { offset: 0, color: '#FFD700' },\n                    { offset: 0.5, color: '#FFCA28' },\n                    { offset: 1, color: '#FFA726' }\n                  ]),\n                  shadowBlur: 10,\n                  shadowColor: 'rgba(255, 215, 0, 0.5)'\n                }\n              },\n              {\n                value: 100 - this.femaleRatio,\n                itemStyle: {\n                  color: 'rgba(255, 255, 255, 0.08)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            labelLine: {\n              show: false\n            },\n            silent: true\n          },\n          // 外圈装饰\n          {\n            type: 'pie',\n            radius: ['88%', '90%'],\n            center: ['50%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: 'rgba(255, 215, 0, 0.3)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            silent: true\n          }\n        ]\n      }\n\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n  padding: 20px;\n\n  .ratio-item {\n    position: relative;\n    width: 140px;\n    height: 140px;\n    border: 2px dashed rgba(0, 212, 255, 0.6);\n    border-radius: 12px;\n    padding: 15px;\n    background: rgba(0, 212, 255, 0.05);\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: rgba(0, 212, 255, 0.8);\n      background: rgba(0, 212, 255, 0.1);\n      transform: scale(1.05);\n    }\n\n    &:nth-child(2) {\n      border-color: rgba(255, 215, 0, 0.6);\n      background: rgba(255, 215, 0, 0.05);\n\n      &:hover {\n        border-color: rgba(255, 215, 0, 0.8);\n        background: rgba(255, 215, 0, 0.1);\n      }\n    }\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 10;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}