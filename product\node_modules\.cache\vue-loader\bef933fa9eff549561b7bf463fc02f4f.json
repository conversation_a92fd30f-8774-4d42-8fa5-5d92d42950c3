{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=style&index=0&id=868084e4&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1755673030427}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5nZW5kZXItcmF0aW8tY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgaGVpZ2h0OiAxMDAlOwoKICAucmF0aW8taXRlbSB7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICB3aWR0aDogMTgycHg7CiAgICBoZWlnaHQ6IDE3MnB4OwoKICAgIC5jaGFydC1jb250YWluZXIgewogICAgICB3aWR0aDogMTAwJTsKICAgICAgaGVpZ2h0OiAxMDAlOwogICAgfQoKICAgIC5yYXRpby1sYWJlbCB7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgdG9wOiA1MCU7CiAgICAgIGxlZnQ6IDUwJTsKICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7CiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgY29sb3I6ICNmZmY7CiAgICAgIHotaW5kZXg6IDEwMDsKICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7CgogICAgICAucGVyY2VudGFnZSB7CiAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgZm9udC1zaXplOiAyOHB4OwogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgIGxpbmUtaGVpZ2h0OiAxOwogICAgICAgIG1hcmdpbi1ib3R0b206IDZweDsKICAgICAgICB0ZXh0LXNoYWRvdzogMCAwIDhweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7CiAgICAgIH0KCiAgICAgIC5nZW5kZXItdGV4dCB7CiAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgIG9wYWNpdHk6IDAuOTsKICAgICAgICBmb250LXdlaWdodDogNTAwOwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["GenderRatioChart.vue"], "names": [], "mappings": ";AA2PA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "GenderRatioChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 70\n    },\n    femaleRatio: {\n      type: Number,\n      default: 30\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.maleChart = echarts.init(chartContainer)\n\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.maleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#00D4FF' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(0, 212, 255, 0.1)',\n                borderWidth: 1\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.femaleChart = echarts.init(chartContainer)\n\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.femaleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#FFD700' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(255, 215, 0, 0.1)',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n\n  .ratio-item {\n    position: relative;\n    width: 182px;\n    height: 172px;\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 100;\n      pointer-events: none;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}