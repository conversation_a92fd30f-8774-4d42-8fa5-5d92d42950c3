{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=style&index=0&id=868084e4&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1755662142482}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5nZW5kZXItcmF0aW8tY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgaGVpZ2h0OiAxMDAlOwogIHBhZGRpbmc6IDIwcHg7CgogIC5yYXRpby1pdGVtIHsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIHdpZHRoOiAxNDBweDsKICAgIGhlaWdodDogMTQwcHg7CiAgICBib3JkZXI6IDJweCBkYXNoZWQgcmdiYSgwLCAyMTIsIDI1NSwgMC42KTsKICAgIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgICBwYWRkaW5nOiAxNXB4OwogICAgYmFja2dyb3VuZDogcmdiYSgwLCAyMTIsIDI1NSwgMC4wNSk7CiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwoKICAgICY6aG92ZXIgewogICAgICBib3JkZXItY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuOCk7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMjEyLCAyNTUsIDAuMSk7CiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7CiAgICB9CgogICAgJjpudGgtY2hpbGQoMikgewogICAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyMTUsIDAsIDAuNik7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyMTUsIDAsIDAuMDUpOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjE1LCAwLCAwLjgpOwogICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyMTUsIDAsIDAuMSk7CiAgICAgIH0KICAgIH0KCiAgICAuY2hhcnQtY29udGFpbmVyIHsKICAgICAgd2lkdGg6IDEwMCU7CiAgICAgIGhlaWdodDogMTAwJTsKICAgIH0KCiAgICAucmF0aW8tbGFiZWwgewogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIHRvcDogNTAlOwogICAgICBsZWZ0OiA1MCU7CiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpOwogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgIGNvbG9yOiAjZmZmOwogICAgICB6LWluZGV4OiAxMDsKCiAgICAgIC5wZXJjZW50YWdlIHsKICAgICAgICBkaXNwbGF5OiBibG9jazsKICAgICAgICBmb250LXNpemU6IDI4cHg7CiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgbGluZS1oZWlnaHQ6IDE7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogNnB4OwogICAgICAgIHRleHQtc2hhZG93OiAwIDAgMTBweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7CiAgICAgIH0KCiAgICAgIC5nZW5kZXItdGV4dCB7CiAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgIG9wYWNpdHk6IDAuOTsKICAgICAgICBmb250LXdlaWdodDogNTAwOwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["GenderRatioChart.vue"], "names": [], "mappings": ";AAyMA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "GenderRatioChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 70\n    },\n    femaleRatio: {\n      type: Number,\n      default: 30\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.maleChart = echarts.init(chartContainer)\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          {\n            type: 'pie',\n            radius: ['70%', '85%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: [\n              {\n                value: this.maleRatio,\n                itemStyle: {\n                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [\n                    { offset: 0, color: '#00D4FF' },\n                    { offset: 0.5, color: '#26C6DA' },\n                    { offset: 1, color: '#4FC3F7' }\n                  ]),\n                  shadowBlur: 10,\n                  shadowColor: 'rgba(0, 212, 255, 0.5)'\n                }\n              },\n              {\n                value: 100 - this.maleRatio,\n                itemStyle: {\n                  color: 'rgba(255, 255, 255, 0.08)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            labelLine: {\n              show: false\n            },\n            silent: true\n          },\n          // 外圈装饰\n          {\n            type: 'pie',\n            radius: ['88%', '90%'],\n            center: ['50%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: 'rgba(0, 212, 255, 0.3)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            silent: true\n          }\n        ]\n      }\n\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n\n      this.femaleChart = echarts.init(chartContainer)\n\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          {\n            type: 'pie',\n            radius: ['70%', '85%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: [\n              {\n                value: this.femaleRatio,\n                itemStyle: {\n                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [\n                    { offset: 0, color: '#FFD700' },\n                    { offset: 0.5, color: '#FFCA28' },\n                    { offset: 1, color: '#FFA726' }\n                  ]),\n                  shadowBlur: 10,\n                  shadowColor: 'rgba(255, 215, 0, 0.5)'\n                }\n              },\n              {\n                value: 100 - this.femaleRatio,\n                itemStyle: {\n                  color: 'rgba(255, 255, 255, 0.08)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            labelLine: {\n              show: false\n            },\n            silent: true\n          },\n          // 外圈装饰\n          {\n            type: 'pie',\n            radius: ['88%', '90%'],\n            center: ['50%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: 'rgba(255, 215, 0, 0.3)'\n                }\n              }\n            ],\n            label: {\n              show: false\n            },\n            silent: true\n          }\n        ]\n      }\n\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n  padding: 20px;\n\n  .ratio-item {\n    position: relative;\n    width: 140px;\n    height: 140px;\n    border: 2px dashed rgba(0, 212, 255, 0.6);\n    border-radius: 12px;\n    padding: 15px;\n    background: rgba(0, 212, 255, 0.05);\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: rgba(0, 212, 255, 0.8);\n      background: rgba(0, 212, 255, 0.1);\n      transform: scale(1.05);\n    }\n\n    &:nth-child(2) {\n      border-color: rgba(255, 215, 0, 0.6);\n      background: rgba(255, 215, 0, 0.05);\n\n      &:hover {\n        border-color: rgba(255, 215, 0, 0.8);\n        background: rgba(255, 215, 0, 0.1);\n      }\n    }\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 10;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}