{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue", "mtime": 1755661043979}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkBA;EACAA,0BADA;EAEAC;IACAC;MACAC,WADA;MAEAC;IAFA,CADA;IAKA;IACAC;MACAF,YADA;MAEAC;IAFA,CANA;IAUA;IACAE;MACAH,YADA;MAEAC;IAFA,CAXA;IAeA;IACAG;MACAJ,YADA;MAEAC;IAFA,CAhBA;IAoBA;IACAI;MACAL,YADA;MAEAC;IAFA;EArBA,CAFA;EA4BAK;IACA;IACAC;MACA;IACA,CAJA;;IAKA;IACAC;MACA;MACA;QACA;MACA,CAJA,CAKA;;;MACA;MACA,yEAPA,CAQA;;MACA;MACAC;MACA;IACA;;EAlBA,CA5BA;EAgDAC;IACA;IACAC;MACA;;MACA;QACAC;MACA;;MACA;IACA,CARA;;IASA;IACAC;MACA,yBADA,CAEA;;MACA,2FAHA,CAIA;;MACAJ,sHALA,CAMA;;MACA;IACA;;EAlBA;AAhDA", "names": ["name", "props", "chartData", "type", "default", "segmentValue", "maxSegments", "maxValue", "maxValueScale", "computed", "totalSegments", "dynamicMaxValue", "console", "methods", "getSegments", "segments", "getFilledSegments"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["HorizontalBarChart.vue"], "sourcesContent": ["<template>\n  <div class=\"horizontal-bar-chart\">\n    <div class=\"chart-container\">\n      <div v-for=\"(item, index) in chartData\" :key=\"index\" class=\"chart-row\">\n        <div class=\"row-label\">{{ item.name }}</div>\n        <div class=\"row-bar-container\">\n          <div class=\"row-bar\">\n            <div v-for=\"(segment, segIndex) in getSegments(item.value)\" :key=\"segIndex\" class=\"bar-segment\"\n              :class=\"{ 'filled': segIndex < getFilledSegments(item.value) }\"></div>\n          </div>\n          <div class=\"row-value\">{{ item.value }}人</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HorizontalBarChart',\n  props: {\n    chartData: {\n      type: Array,\n      default: () => []\n    },\n    // 每个分段代表的数值\n    segmentValue: {\n      type: Number,\n      default: 10\n    },\n    // 最大分段数（固定总分段数，不随数据变化）\n    maxSegments: {\n      type: Number,\n      default: 25\n    },\n    // 进度条的最大值（用于计算比例，如果不设置则自动计算）\n    maxValue: {\n      type: Number,\n      default: null\n    },\n    // 最大值的缩放比例（数据最大值的倍数）\n    maxValueScale: {\n      type: Number,\n      default: 1.1\n    }\n  },\n  computed: {\n    // 计算总分段数（固定值，不随数据变化）\n    totalSegments () {\n      return this.maxSegments\n    },\n    // 动态计算的最大值\n    dynamicMaxValue () {\n      // 如果手动设置了maxValue，则使用设置的值\n      if (this.maxValue !== null) {\n        return this.maxValue\n      }\n      // 否则根据数据自动计算\n      if (!this.chartData.length) return 100\n      const dataMaxValue = Math.max(...this.chartData.map(item => item.value))\n      // 返回数据最大值的倍数，确保最大值不会撑满\n      const calculatedMax = Math.ceil(dataMaxValue * this.maxValueScale)\n      console.log(`数据最大值: ${dataMaxValue}, 缩放比例: ${this.maxValueScale}, 计算的最大值: ${calculatedMax}`)\n      return calculatedMax\n    }\n  },\n  methods: {\n    // 生成分段数组\n    getSegments (value) {\n      const segments = []\n      for (let i = 0; i < this.totalSegments; i++) {\n        segments.push(i)\n      }\n      return segments\n    },\n    // 计算应该填充的分段数\n    getFilledSegments (value) {\n      if (value <= 0) return 0\n      // 计算按比例应该填充的分段数\n      const proportionalSegments = Math.floor((value / this.dynamicMaxValue) * this.totalSegments)\n      // 调试信息\n      console.log(`值: ${value}, 动态最大值: ${this.dynamicMaxValue}, 总分段: ${this.totalSegments}, 计算分段: ${proportionalSegments}`)\n      // 确保有数值的项目至少显示1个分段\n      return Math.max(1, proportionalSegments)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.horizontal-bar-chart {\n  width: 100%;\n  height: 100%;\n  padding-top: 10px;\n}\n\n.chart-container {\n  display: flex;\n  flex-direction: column;\n  gap: 22px;\n}\n\n.chart-row {\n  display: flex;\n  align-items: center;\n  position: relative;\n}\n\n.row-label {\n  width: 50px;\n  color: #FFFFFF;\n  font-size: 14px;\n  font-weight: 500;\n  text-align: left;\n  flex-shrink: 0;\n  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);\n}\n\n.row-bar-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  margin-left: 15px;\n}\n\n.row-bar {\n  flex: 1;\n  height: 15px;\n  display: flex;\n  align-items: center;\n  background: #09306B;\n  border: 1px solid #979797;\n  position: relative;\n  overflow: hidden;\n  padding: 1px;\n  /* 右边斜切效果 */\n  /* clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 0 100%); */\n}\n\n.bar-segment {\n  flex: 1;\n  height: 10px;\n  background: rgba(0, 100, 200, 0.2);\n  transition: all 0.4s ease;\n  position: relative;\n  border: none;\n  /* 倾斜的格子效果 */\n  clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%);\n  margin-right: 1px;\n}\n\n.bar-segment:last-child {\n  margin-right: 0;\n}\n\n.bar-segment.filled {\n  background: linear-gradient(135deg,\n      #00D4FF 0%,\n      #0099CC 30%,\n      #00AADD 70%,\n      #33E0FF 100%);\n}\n\n.row-value {\n  color: #FFFFFF;\n  font-size: 14px;\n  width: 70px;\n  text-align: right;\n}\n\n/* 悬停效果 */\n.chart-row:hover .row-bar {\n  border-color: rgba(0, 212, 255, 0.8);\n  box-shadow:\n    inset 0 1px 2px rgba(0, 0, 0, 0.3),\n    0 0 15px rgba(0, 212, 255, 0.4);\n}\n\n.chart-row:hover .bar-segment.filled {\n  background: linear-gradient(135deg,\n      #33E0FF 0%,\n      #00AADD 30%,\n      #00BBEE 70%,\n      #55F0FF 100%);\n  box-shadow:\n    0 0 12px rgba(0, 212, 255, 0.7),\n    inset 0 1px 2px rgba(255, 255, 255, 0.4);\n  transform: scale(1.05);\n}\n\n.chart-row:hover .row-value {\n  color: #00D4FF;\n  text-shadow: 0 0 8px rgba(0, 212, 255, 0.6);\n}\n\n.chart-row:hover .row-label {\n  color: #00D4FF;\n  text-shadow: 0 0 6px rgba(0, 212, 255, 0.4);\n}\n</style>\n"]}]}