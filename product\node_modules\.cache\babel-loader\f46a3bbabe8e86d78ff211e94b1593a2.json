{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755662087815}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA+GA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC,cADA;IAEAC,QAFA;IAGAC,UAHA;IAIAC,QAJA;IAKAC,kBALA;IAMAC;EANA,CAFA;;EAUAC;IACA;MACAC,eADA;MAEA;MACAC,gBACA;QAAAV;QAAAW;MAAA,CADA,EAEA;QAAAX;QAAAW;MAAA,CAFA,EAGA;QAAAX;QAAAW;MAAA,CAHA,EAIA;QAAAX;QAAAW;MAAA,CAJA,EAKA;QAAAX;QAAAW;MAAA,CALA,EAMA;QAAAX;QAAAW;MAAA,CANA,CAHA;MAWA;MACAC,YACA;QAAAZ;QAAAW;QAAAE;QAAAC;MAAA,CADA,EAEA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CAFA,EAGA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CAHA,EAIA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CAJA,EAKA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CALA,EAMA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CANA,EAOA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CAPA,EAQA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CARA,EASA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CATA,EAUA;QAAAd;QAAAW;QAAAE;QAAAC;MAAA,CAVA,CAZA;MAwBA;MACAC,qBACA;QAAAf;QAAAW;MAAA,CADA,EAEA;QAAAX;QAAAW;MAAA,CAFA,EAGA;QAAAX;QAAAW;MAAA,CAHA,EAIA;QAAAX;QAAAW;MAAA,CAJA,EAKA;QAAAX;QAAAW;MAAA,CALA,EAMA;QAAAX;QAAAW;MAAA,CANA,EAOA;QAAAX;QAAAW;MAAA,CAPA,EAQA;QAAAX;QAAAW;MAAA,CARA,EASA;QAAAX;QAAAW;MAAA,CATA,EAUA;QAAAX;QAAAW;MAAA,CAVA,EAWA;QAAAX;QAAAW;MAAA,CAXA,EAYA;QAAAX;QAAAW;MAAA,CAZA,EAaA;QAAAX;QAAAW;MAAA,CAbA,EAcA;QAAAX;QAAAW;MAAA,CAdA,EAeA;QAAAX;QAAAW;MAAA,CAfA,EAgBA;QAAAX;QAAAW;MAAA,CAhBA,EAiBA;QAAAX;QAAAW;MAAA,CAjBA,EAkBA;QAAAX;QAAAW;MAAA,CAlBA,EAmBA;QAAAX;QAAAW;MAAA,CAnBA,EAoBA;QAAAX;QAAAW;MAAA,CApBA,EAqBA;QAAAX;QAAAW;MAAA,CArBA,EAsBA;QAAAX;QAAAW;MAAA,CAtBA,EAuBA;QAAAX;QAAAW;MAAA,CAvBA,EAwBA;QAAAX;QAAAW;MAAA,CAxBA,EAyBA;QAAAX;QAAAW;MAAA,CAzBA,EA0BA;QAAAX;QAAAW;MAAA,CA1BA,EA2BA;QAAAX;QAAAW;MAAA,CA3BA,EA4BA;QAAAX;QAAAW;MAAA,CA5BA,EA6BA;QAAAX;QAAAW;MAAA,CA7BA,EA8BA;QAAAX;QAAAW;MAAA,CA9BA,EA+BA;QAAAX;QAAAW;MAAA,CA/BA,EAgCA;QAAAX;QAAAW;MAAA,CAhCA,EAiCA;QAAAX;QAAAW;MAAA,CAjCA,EAkCA;QAAAX;QAAAW;MAAA,CAlCA,CAzBA;MA6DA;MACAK,sBACA;QAAAhB;QAAAW;MAAA,CADA,EAEA;QAAAX;QAAAW;MAAA,CAFA,EAGA;QAAAX;QAAAW;MAAA,CAHA,EAIA;QAAAX;QAAAW;MAAA,CAJA,EAKA;QAAAX;QAAAW;MAAA,CALA,EAMA;QAAAX;QAAAW;MAAA,CANA,EAOA;QAAAX;QAAAW;MAAA,CAPA,EAQA;QAAAX;QAAAW;MAAA,CARA,EASA;QAAAX;QAAAW;MAAA,CATA,EAUA;QAAAX;QAAAW;MAAA,CAVA,EAWA;QAAAX;QAAAW;MAAA,CAXA,EAYA;QAAAX;QAAAW;MAAA,CAZA,EAaA;QAAAX;QAAAW;MAAA,CAbA,EAcA;QAAAX;QAAAW;MAAA,CAdA,EAeA;QAAAX;QAAAW;MAAA,CAfA,EAgBA;QAAAX;QAAAW;MAAA,CAhBA,EAiBA;QAAAX;QAAAW;MAAA,CAjBA,EAkBA;QAAAX;QAAAW;MAAA,CAlBA,EAmBA;QAAAX;QAAAW;MAAA,CAnBA,CA9DA;MAmFA;MACAM,eACA;QAAAjB;QAAAW;QAAAE;MAAA,CADA,EAEA;QAAAb;QAAAW;QAAAE;MAAA,CAFA,EAGA;QAAAb;QAAAW;QAAAE;MAAA,CAHA,EAIA;QAAAb;QAAAW;QAAAE;MAAA,CAJA,EAKA;QAAAb;QAAAW;QAAAE;MAAA,CALA,CApFA;MA2FAK;IA3FA;EA6FA,CAxGA;;EAyGAC,YAzGA;;EA2GAC;IACA;IACA;IACA;EACA,CA/GA;;EAgHAC;IACA;MACAC;IACA;EACA,CApHA;;EAqHAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBA;IACAC;MACA;QAAAC;MAAA;IACA;;EApBA;AArHA", "names": ["name", "components", "BarScrollChart", "<PERSON><PERSON><PERSON>", "PieChart3D", "<PERSON><PERSON><PERSON>", "HorizontalBarChart", "GenderRatioChart", "data", "currentTime", "educationData", "value", "partyData", "percentage", "color", "sectorAnalysisData", "discussionGroupData", "ageChartData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "goHome", "path"], "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sources": ["committeeStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn area-select-btn\">\r\n            <span>选择地区</span>\r\n          </div>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">418</div>\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\">42</div>\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" :male-ratio=\"70\" :female-ratio=\"30\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" :chart-data=\"ageChartData\" :name=\"ageChartName\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '研究生', value: 84 },\r\n        { name: '本科', value: 165 },\r\n        { name: '大专', value: 500 },\r\n        { name: '高中', value: 200 },\r\n        { name: '职高', value: 160 },\r\n        { name: '初中', value: 90 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [\r\n        { name: '第1组', value: 65 },\r\n        { name: '第2组', value: 42 },\r\n        { name: '第3组', value: 63 },\r\n        { name: '第4组', value: 45 },\r\n        { name: '第5组', value: 68 },\r\n        { name: '第6组', value: 38 },\r\n        { name: '第7组', value: 41 },\r\n        { name: '第8组', value: 39 },\r\n        { name: '第9组', value: 43 },\r\n        { name: '第10组', value: 58 },\r\n        { name: '第11组', value: 36 },\r\n        { name: '第12组', value: 15 },\r\n        { name: '第13组', value: 55 },\r\n        { name: '第14组', value: 42 },\r\n        { name: '第15组', value: 66 },\r\n        { name: '第16组', value: 35 },\r\n        { name: '第17组', value: 28 },\r\n        { name: '第18组', value: 40 },\r\n        { name: '第19组', value: 48 }\r\n      ],\r\n      // 年龄数据\r\n      ageChartData: [\r\n        { name: '29岁以下', value: 20, percentage: '5%' },\r\n        { name: '30-39岁', value: 125, percentage: '30%' },\r\n        { name: '40-49岁', value: 168, percentage: '40%' },\r\n        { name: '50-59岁', value: 85, percentage: '20%' },\r\n        { name: '60岁以上', value: 20, percentage: '5%' }\r\n      ],\r\n      ageChartName: '年龄占比'\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          padding: 8px 20px;\r\n          border-radius: 4px;\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          color: #FFFFFF;\r\n          background: linear-gradient(135deg, rgba(0, 181, 254, 0.8) 0%, rgba(0, 100, 200, 0.8) 100%);\r\n          border: 1px solid rgba(0, 181, 254, 0.6);\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: linear-gradient(135deg, rgba(255, 214, 0, 0.9) 0%, rgba(255, 165, 0, 0.9) 100%);\r\n            border-color: rgba(255, 214, 0, 0.8);\r\n            color: #333;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(255, 214, 0, 1) 0%, rgba(255, 165, 0, 1) 100%);\r\n              border-color: rgba(255, 214, 0, 1);\r\n            }\r\n          }\r\n\r\n          span {\r\n            position: relative;\r\n            z-index: 1;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-size: 48px;\r\n              font-weight: bold;\r\n              color: #00D4FF;\r\n              margin-bottom: 10px;\r\n              text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}