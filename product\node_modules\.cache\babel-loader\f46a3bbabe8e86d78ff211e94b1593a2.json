{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755676681593}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkIA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC,cADA;IAEAC,QAFA;IAGAC,UAHA;IAIAC,QAJA;IAKAC,kBALA;IAMAC;EANA,CAFA;;EAUAC;IACA;MACAC,eADA;MAEA;MACAC,sBAHA;MAIAC,kBAJA;MAKAC,wBALA;MAMAC;QACAC,oBADA;QAEAC;MAFA,CANA;MAUAC,WACA;QACAhB,UADA;QAEAiB,eAFA;QAGAH,WACA;UAAAd;UAAAiB;QAAA,CADA,EAEA;UAAAjB;UAAAiB;QAAA,CAFA,EAGA;UAAAjB;UAAAiB;QAAA,CAHA,EAIA;UAAAjB;UAAAiB;QAAA,CAJA,EAKA;UAAAjB;UAAAiB;QAAA,CALA,EAMA;UAAAjB;UAAAiB;QAAA,CANA,EAOA;UAAAjB;UAAAiB;QAAA,CAPA,EAQA;UAAAjB;UAAAiB;QAAA,CARA,EASA;UAAAjB;UAAAiB;QAAA,CATA,EAUA;UAAAjB;UAAAiB;QAAA,CAVA;MAHA,CADA,CAVA;MA4BA;MACAC,gBACA;QAAAlB;QAAAmB;MAAA,CADA,EAEA;QAAAnB;QAAAmB;MAAA,CAFA,EAGA;QAAAnB;QAAAmB;MAAA,CAHA,EAIA;QAAAnB;QAAAmB;MAAA,CAJA,EAKA;QAAAnB;QAAAmB;MAAA,CALA,EAMA;QAAAnB;QAAAmB;MAAA,CANA,CA7BA;MAqCA;MACAC,YACA;QAAApB;QAAAmB;QAAAE;QAAAC;MAAA,CADA,EAEA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CAFA,EAGA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CAHA,EAIA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CAJA,EAKA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CALA,EAMA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CANA,EAOA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CAPA,EAQA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CARA,EASA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CATA,EAUA;QAAAtB;QAAAmB;QAAAE;QAAAC;MAAA,CAVA,CAtCA;MAkDA;MACAC,qBACA;QAAAvB;QAAAmB;MAAA,CADA,EAEA;QAAAnB;QAAAmB;MAAA,CAFA,EAGA;QAAAnB;QAAAmB;MAAA,CAHA,EAIA;QAAAnB;QAAAmB;MAAA,CAJA,EAKA;QAAAnB;QAAAmB;MAAA,CALA,EAMA;QAAAnB;QAAAmB;MAAA,CANA,EAOA;QAAAnB;QAAAmB;MAAA,CAPA,EAQA;QAAAnB;QAAAmB;MAAA,CARA,EASA;QAAAnB;QAAAmB;MAAA,CATA,EAUA;QAAAnB;QAAAmB;MAAA,CAVA,EAWA;QAAAnB;QAAAmB;MAAA,CAXA,EAYA;QAAAnB;QAAAmB;MAAA,CAZA,EAaA;QAAAnB;QAAAmB;MAAA,CAbA,EAcA;QAAAnB;QAAAmB;MAAA,CAdA,EAeA;QAAAnB;QAAAmB;MAAA,CAfA,EAgBA;QAAAnB;QAAAmB;MAAA,CAhBA,EAiBA;QAAAnB;QAAAmB;MAAA,CAjBA,EAkBA;QAAAnB;QAAAmB;MAAA,CAlBA,EAmBA;QAAAnB;QAAAmB;MAAA,CAnBA,EAoBA;QAAAnB;QAAAmB;MAAA,CApBA,EAqBA;QAAAnB;QAAAmB;MAAA,CArBA,EAsBA;QAAAnB;QAAAmB;MAAA,CAtBA,EAuBA;QAAAnB;QAAAmB;MAAA,CAvBA,EAwBA;QAAAnB;QAAAmB;MAAA,CAxBA,EAyBA;QAAAnB;QAAAmB;MAAA,CAzBA,EA0BA;QAAAnB;QAAAmB;MAAA,CA1BA,EA2BA;QAAAnB;QAAAmB;MAAA,CA3BA,EA4BA;QAAAnB;QAAAmB;MAAA,CA5BA,EA6BA;QAAAnB;QAAAmB;MAAA,CA7BA,EA8BA;QAAAnB;QAAAmB;MAAA,CA9BA,EA+BA;QAAAnB;QAAAmB;MAAA,CA/BA,EAgCA;QAAAnB;QAAAmB;MAAA,CAhCA,EAiCA;QAAAnB;QAAAmB;MAAA,CAjCA,EAkCA;QAAAnB;QAAAmB;MAAA,CAlCA,CAnDA;MAuFA;MACAK,sBACA;QAAAxB;QAAAmB;MAAA,CADA,EAEA;QAAAnB;QAAAmB;MAAA,CAFA,EAGA;QAAAnB;QAAAmB;MAAA,CAHA,EAIA;QAAAnB;QAAAmB;MAAA,CAJA,EAKA;QAAAnB;QAAAmB;MAAA,CALA,EAMA;QAAAnB;QAAAmB;MAAA,CANA,EAOA;QAAAnB;QAAAmB;MAAA,CAPA,EAQA;QAAAnB;QAAAmB;MAAA,CARA,EASA;QAAAnB;QAAAmB;MAAA,CATA,EAUA;QAAAnB;QAAAmB;MAAA,CAVA,EAWA;QAAAnB;QAAAmB;MAAA,CAXA,EAYA;QAAAnB;QAAAmB;MAAA,CAZA,EAaA;QAAAnB;QAAAmB;MAAA,CAbA,EAcA;QAAAnB;QAAAmB;MAAA,CAdA,EAeA;QAAAnB;QAAAmB;MAAA,CAfA,EAgBA;QAAAnB;QAAAmB;MAAA,CAhBA,EAiBA;QAAAnB;QAAAmB;MAAA,CAjBA,EAkBA;QAAAnB;QAAAmB;MAAA,CAlBA,EAmBA;QAAAnB;QAAAmB;MAAA,CAnBA,CAxFA;MA6GA;MACAM,eACA;QAAAzB;QAAAmB;QAAAE;MAAA,CADA,EAEA;QAAArB;QAAAmB;QAAAE;MAAA,CAFA,EAGA;QAAArB;QAAAmB;QAAAE;MAAA,CAHA,EAIA;QAAArB;QAAAmB;QAAAE;MAAA,CAJA,EAKA;QAAArB;QAAAmB;QAAAE;MAAA,CALA,CA9GA;MAqHAK,oBArHA;MAsHAC,mBAtHA;MAuHAC;IAvHA;EAyHA,CApIA;;EAqIAC,YArIA;;EAuIAC;IACA;IACA;IACA;EACA,CA3IA;;EA4IAC;IACA;MACAC;IACA;EACA,CAhJA;;EAiJAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBA;IACAC;MACA;QAAAC;MAAA;IACA,CApBA;;IAqBA;IACAC;MACA;MACA;MACA;MACA,6BAJA,CAKA;;MACAC;IACA;;EA7BA;AAjJA", "names": ["name", "components", "BarScrollChart", "<PERSON><PERSON><PERSON>", "PieChart3D", "<PERSON><PERSON><PERSON>", "HorizontalBarChart", "GenderRatioChart", "data", "currentTime", "showAreaPopover", "<PERSON><PERSON><PERSON>", "selectedDistrictCode", "treeProps", "children", "label", "treeData", "code", "educationData", "value", "partyData", "percentage", "color", "sectorAnalysisData", "discussionGroupData", "ageChartData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "memberTotalNum", "standingMemberTotalNum", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "goHome", "path", "handleNodeClick", "console"], "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sources": ["committeeStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"xyl-region\">\r\n            <el-popover placement=\"bottom-start\" trigger=\"click\" transition=\"zy-el-zoom-in-top\"\r\n              popper-class=\"xyl-region-popover\" v-model=\"showAreaPopover\">\r\n              <el-scrollbar class=\"xyl-region-tree\">\r\n                <el-tree ref=\"treeRef\" highlight-current :data=\"treeData\" :props=\"treeProps\" node-key=\"code\"\r\n                  @node-click=\"handleNodeClick\" :default-expanded-keys=\"['qingdao']\" />\r\n              </el-scrollbar>\r\n              <!-- <el-scrollbar class=\"region-tree\">\r\n              <el-tree :data=\"treeData\" :props=\"treeProps\" highlight-current node-key=\"code\"\r\n                :default-expanded-keys=\"['qingdao']\" :current-node-key=\"selectedDistrictCode\"\r\n                @node-click=\"handleNodeClick\" class=\"area-tree\">\r\n              </el-tree>\r\n            </el-scrollbar> -->\r\n              <div class=\"header-btn area-select-btn\" slot=\"reference\">\r\n                <span>{{ selectedArea }}</span>\r\n                <i class=\"dropdown-icon\" :class=\"{ 'active': showAreaPopover }\">▼</i>\r\n              </div>\r\n            </el-popover>\r\n          </div>\r\n\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #02FBFB;\">{{ memberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_member.png\" class=\"count-img\">\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #F5E74F;\">{{ standingMemberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_standingMember.png\" class=\"count-img\">\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" :male-ratio=\"100\" :female-ratio=\"60\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" :chart-data=\"ageChartData\" :name=\"ageChartName\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      selectedArea: '青岛',\r\n      selectedDistrictCode: '',\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'name'\r\n      },\r\n      treeData: [\r\n        {\r\n          name: '青岛',\r\n          code: 'qingdao',\r\n          children: [\r\n            { name: '市南区', code: 'shinan' },\r\n            { name: '市北区', code: 'shibei' },\r\n            { name: '李沧区', code: 'licang' },\r\n            { name: '崂山区', code: 'laoshan' },\r\n            { name: '城阳区', code: 'chengyang' },\r\n            { name: '即墨区', code: 'jimo' },\r\n            { name: '胶州市', code: 'jiaozhou' },\r\n            { name: '平度市', code: 'pingdu' },\r\n            { name: '莱西市', code: 'laixi' },\r\n            { name: '西海岸新区', code: 'xihaian' }\r\n          ]\r\n        }\r\n      ],\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '研究生', value: 84 },\r\n        { name: '本科', value: 165 },\r\n        { name: '大专', value: 500 },\r\n        { name: '高中', value: 200 },\r\n        { name: '职高', value: 160 },\r\n        { name: '初中', value: 90 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [\r\n        { name: '第1组', value: 65 },\r\n        { name: '第2组', value: 42 },\r\n        { name: '第3组', value: 63 },\r\n        { name: '第4组', value: 45 },\r\n        { name: '第5组', value: 68 },\r\n        { name: '第6组', value: 38 },\r\n        { name: '第7组', value: 41 },\r\n        { name: '第8组', value: 39 },\r\n        { name: '第9组', value: 43 },\r\n        { name: '第10组', value: 58 },\r\n        { name: '第11组', value: 36 },\r\n        { name: '第12组', value: 15 },\r\n        { name: '第13组', value: 55 },\r\n        { name: '第14组', value: 42 },\r\n        { name: '第15组', value: 66 },\r\n        { name: '第16组', value: 35 },\r\n        { name: '第17组', value: 28 },\r\n        { name: '第18组', value: 40 },\r\n        { name: '第19组', value: 48 }\r\n      ],\r\n      // 年龄数据\r\n      ageChartData: [\r\n        { name: '29岁以下', value: 20, percentage: '5%' },\r\n        { name: '30-39岁', value: 125, percentage: '30%' },\r\n        { name: '40-49岁', value: 168, percentage: '40%' },\r\n        { name: '50-59岁', value: 85, percentage: '20%' },\r\n        { name: '60岁以上', value: 20, percentage: '5%' }\r\n      ],\r\n      ageChartName: '年龄占比',\r\n      memberTotalNum: 418,\r\n      standingMemberTotalNum: 42\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 允许选择所有节点（包括父级青岛）\r\n      this.selectedArea = data.name\r\n      this.selectedDistrictCode = data.code\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换地区后的数据更新逻辑\r\n      console.log('选择了地区:', data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n\r\n        .xyl-region {\r\n          display: inline-block;\r\n          margin: auto;\r\n          padding: 0 6px;\r\n\r\n          .xyl-region-view {\r\n            display: flex;\r\n            align-items: center;\r\n            height: var(--zy-height);\r\n\r\n            .xyl-region-img {\r\n              width: 24px;\r\n              height: 24px;\r\n            }\r\n\r\n            .xyl-region-name {\r\n              color: #fff;\r\n              padding: 0 6px;\r\n              line-height: var(--zy-line-height);\r\n              font-size: var(--zy-name-font-size);\r\n            }\r\n\r\n            .xyl-region-icon {\r\n              width: 24px;\r\n              color: #fff;\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .el-icon {\r\n                transition: all 0.6s;\r\n                transform: rotateZ(0deg);\r\n              }\r\n            }\r\n\r\n            .xyl-region-is-icon {\r\n              .el-icon {\r\n                transition: all 0.6s;\r\n                transform: rotateZ(-180deg);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .xyl-region-popover {\r\n          width: 290px !important;\r\n          padding: 0 !important;\r\n\r\n          .el-scrollbar__wrap {\r\n            max-height: 220px;\r\n          }\r\n\r\n          .el-scrollbar__view {\r\n            padding: var(--zy-distance-five) 0;\r\n          }\r\n\r\n          .xyl-region-tree {\r\n            width: 100%;\r\n\r\n            .el-tree-node.is-current {\r\n              &>.el-tree-node__content {\r\n                .el-tree-node__label {\r\n                  color: var(--zy-el-color-primary);\r\n                }\r\n              }\r\n            }\r\n\r\n            .el-tree-node__content {\r\n              height: var(--zy-height);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-weight: 500;\r\n              font-size: 32px;\r\n            }\r\n\r\n            .count-img {\r\n              width: 118px;\r\n              margin-top: -10px;\r\n              margin-bottom: 10px;\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// el-popover 自定义样式 - 使用更高优先级\r\n.el-popover.area-popover {\r\n  width: 290px !important;\r\n  padding: 0 !important;\r\n  background: rgba(7, 52, 95, 0.95) !important;\r\n  border: 1px solid rgba(0, 181, 254, 0.5) !important;\r\n  border-radius: 8px !important;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\r\n\r\n  .area-tree-container {\r\n    padding: 8px 0;\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n\r\n    .area-tree {\r\n      background: transparent;\r\n      color: #FFFFFF;\r\n\r\n      // 树节点样式\r\n      .el-tree-node {\r\n        margin-bottom: 4px;\r\n\r\n        .el-tree-node__content {\r\n          background: transparent;\r\n          color: #FFFFFF;\r\n          height: 40px;\r\n          line-height: 40px;\r\n          border-radius: 6px;\r\n          padding: 0 12px;\r\n          transition: all 0.3s ease;\r\n          border: 1px solid transparent;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.15) 0%, rgba(0, 120, 220, 0.15) 100%);\r\n            border-color: rgba(0, 181, 254, 0.3);\r\n            color: #1FC6FF;\r\n            transform: translateX(2px);\r\n          }\r\n\r\n          .el-tree-node__expand-icon {\r\n            color: #1FC6FF;\r\n            font-size: 12px;\r\n            margin-right: 8px;\r\n            transition: all 0.3s ease;\r\n          }\r\n\r\n          .el-tree-node__label {\r\n            color: inherit;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            transition: all 0.3s ease;\r\n          }\r\n        }\r\n\r\n        // 当前选中节点\r\n        &.is-current>.el-tree-node__content {\r\n          background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n          border-color: #1FC6FF;\r\n          color: #1FC6FF;\r\n          font-weight: bold;\r\n          box-shadow: 0 2px 8px rgba(0, 181, 254, 0.2);\r\n\r\n          .el-tree-node__label {\r\n            font-weight: bold;\r\n          }\r\n        }\r\n\r\n        // 父节点样式（青岛）\r\n        &:first-child {\r\n          .el-tree-node__content {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.1) 0%, rgba(0, 120, 220, 0.1) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.2);\r\n            font-weight: 500;\r\n\r\n            .el-tree-node__label {\r\n              font-size: 15px;\r\n              font-weight: 500;\r\n              color: #1FC6FF;\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n              border-color: rgba(0, 181, 254, 0.5);\r\n            }\r\n          }\r\n\r\n          &.is-current>.el-tree-node__content {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.4) 0%, rgba(0, 120, 220, 0.4) 100%);\r\n            border-color: #1FC6FF;\r\n            box-shadow: 0 3px 12px rgba(0, 181, 254, 0.3);\r\n          }\r\n        }\r\n\r\n        // 子节点样式（区县）\r\n        .el-tree-node__children {\r\n          margin-top: 4px;\r\n          padding-left: 20px;\r\n          border-left: 2px solid rgba(0, 181, 254, 0.2);\r\n          margin-left: 12px;\r\n\r\n          .el-tree-node {\r\n            .el-tree-node__content {\r\n              padding-left: 16px;\r\n              height: 36px;\r\n              line-height: 36px;\r\n\r\n              .el-tree-node__label {\r\n                font-size: 13px;\r\n                color: rgba(255, 255, 255, 0.9);\r\n              }\r\n\r\n              &:hover {\r\n                .el-tree-node__label {\r\n                  color: #1FC6FF;\r\n                }\r\n              }\r\n            }\r\n\r\n            &.is-current>.el-tree-node__content {\r\n              .el-tree-node__label {\r\n                color: #1FC6FF;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 隐藏叶子节点的展开图标\r\n      .el-tree-node__expand-icon.is-leaf {\r\n        color: transparent;\r\n        cursor: default;\r\n      }\r\n    }\r\n\r\n    // 自定义滚动条\r\n    &::-webkit-scrollbar {\r\n      width: 6px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-track {\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 3px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-thumb {\r\n      background: linear-gradient(180deg, rgba(0, 181, 254, 0.6) 0%, rgba(0, 120, 220, 0.6) 100%);\r\n      border-radius: 3px;\r\n\r\n      &:hover {\r\n        background: linear-gradient(180deg, rgba(0, 181, 254, 0.8) 0%, rgba(0, 120, 220, 0.8) 100%);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}