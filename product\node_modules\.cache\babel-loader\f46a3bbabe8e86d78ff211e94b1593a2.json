{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755674589822}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdXNlSW5kZXggfSBmcm9tICcuLi9zY3JlZW4uanMnOwppbXBvcnQgQmFyU2Nyb2xsQ2hhcnQgZnJvbSAnLi4vY29tcG9uZW50cy9CYXJTY3JvbGxDaGFydC52dWUnOwppbXBvcnQgQmFyQ2hhcnQgZnJvbSAnLi4vY29tcG9uZW50cy9CYXJDaGFydC52dWUnOwppbXBvcnQgUGllQ2hhcnQzRCBmcm9tICcuLi9jb21wb25lbnRzL1BpZUNoYXJ0M0QudnVlJzsKaW1wb3J0IFBpZUNoYXJ0IGZyb20gJy4uL2NvbXBvbmVudHMvUGllQ2hhcnQudnVlJzsKaW1wb3J0IEhvcml6b250YWxCYXJDaGFydCBmcm9tICcuLi9jb21wb25lbnRzL0hvcml6b250YWxCYXJDaGFydC52dWUnOwppbXBvcnQgR2VuZGVyUmF0aW9DaGFydCBmcm9tICcuLi9jb21wb25lbnRzL0dlbmRlclJhdGlvQ2hhcnQudnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdCaWdTY3JlZW4nLAogIGNvbXBvbmVudHM6IHsKICAgIEJhclNjcm9sbENoYXJ0LAogICAgQmFyQ2hhcnQsCiAgICBQaWVDaGFydDNELAogICAgUGllQ2hhcnQsCiAgICBIb3Jpem9udGFsQmFyQ2hhcnQsCiAgICBHZW5kZXJSYXRpb0NoYXJ0CiAgfSwKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRUaW1lOiAnJywKICAgICAgLy8g5Zyw5Yy66YCJ5oup55u45YWzCiAgICAgIHNob3dBcmVhUG9wb3ZlcjogZmFsc2UsCiAgICAgIGFjdGl2ZVRhYjogJ2NpdHknLAogICAgICBzZWxlY3RlZENpdHk6ICfpnZLlspsnLAogICAgICBzZWxlY3RlZERpc3RyaWN0OiAnJywKICAgICAgY2l0eUxpc3Q6IFt7CiAgICAgICAgbmFtZTogJ+mdkuWymycsCiAgICAgICAgY29kZTogJ3FpbmdkYW8nCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5rWO5Y2XJywKICAgICAgICBjb2RlOiAnamluYW4nCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn54Of5Y+wJywKICAgICAgICBjb2RlOiAneWFudGFpJwogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+a9jeWdiicsCiAgICAgICAgY29kZTogJ3dlaWZhbmcnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5Li05rKCJywKICAgICAgICBjb2RlOiAnbGlueWknCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5reE5Y2aJywKICAgICAgICBjb2RlOiAnemlibycKICAgICAgfV0sCiAgICAgIGRpc3RyaWN0TGlzdDogW3sKICAgICAgICBuYW1lOiAn5biC5Y2X5Yy6JywKICAgICAgICBjb2RlOiAnc2hpbmFuJwogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+W4guWMl+WMuicsCiAgICAgICAgY29kZTogJ3NoaWJlaScKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfmnY7msqfljLonLAogICAgICAgIGNvZGU6ICdsaWNhbmcnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5bSC5bGx5Yy6JywKICAgICAgICBjb2RlOiAnbGFvc2hhbicKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfln47pmLPljLonLAogICAgICAgIGNvZGU6ICdjaGVuZ3lhbmcnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5Y2z5aKo5Yy6JywKICAgICAgICBjb2RlOiAnamltbycKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfog7blt57luIInLAogICAgICAgIGNvZGU6ICdqaWFvemhvdScKICAgICAgfSwgewogICAgICAgIG5hbWU6ICflubPluqbluIInLAogICAgICAgIGNvZGU6ICdwaW5nZHUnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn6I6x6KW/5biCJywKICAgICAgICBjb2RlOiAnbGFpeGknCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn6KW/5rW35bK45paw5Yy6JywKICAgICAgICBjb2RlOiAneGloYWlhbicKICAgICAgfV0sCiAgICAgIC8vIOWtpuWOhuaVsOaNrgogICAgICBlZHVjYXRpb25EYXRhOiBbewogICAgICAgIG5hbWU6ICfnoJTnqbbnlJ8nLAogICAgICAgIHZhbHVlOiA4NAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+acrOenkScsCiAgICAgICAgdmFsdWU6IDE2NQogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+Wkp+S4kycsCiAgICAgICAgdmFsdWU6IDUwMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+mrmOS4rScsCiAgICAgICAgdmFsdWU6IDIwMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+iBjOmrmCcsCiAgICAgICAgdmFsdWU6IDE2MAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+WIneS4rScsCiAgICAgICAgdmFsdWU6IDkwCiAgICAgIH1dLAogICAgICAvLyDlhZrmtL7mlbDmja4KICAgICAgcGFydHlEYXRhOiBbewogICAgICAgIG5hbWU6ICfkuK3lm73lhbHkuqflhZonLAogICAgICAgIHZhbHVlOiAzMiwKICAgICAgICBwZXJjZW50YWdlOiAxNSwKICAgICAgICBjb2xvcjogJyNGRjZCNkInCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5rCR6Z2pJywKICAgICAgICB2YWx1ZTogMTUsCiAgICAgICAgcGVyY2VudGFnZTogOCwKICAgICAgICBjb2xvcjogJyM0RUNEQzQnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5rCR55ufJywKICAgICAgICB2YWx1ZTogMTQsCiAgICAgICAgcGVyY2VudGFnZTogNywKICAgICAgICBjb2xvcjogJyM0NUI3RDEnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5rCR5bu6JywKICAgICAgICB2YWx1ZTogMTMsCiAgICAgICAgcGVyY2VudGFnZTogNiwKICAgICAgICBjb2xvcjogJyM5NkNFQjQnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5rCR6L+bJywKICAgICAgICB2YWx1ZTogMTIsCiAgICAgICAgcGVyY2VudGFnZTogNSwKICAgICAgICBjb2xvcjogJyNGRkVBQTcnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5Yac5bel5YWaJywKICAgICAgICB2YWx1ZTogMTAsCiAgICAgICAgcGVyY2VudGFnZTogNCwKICAgICAgICBjb2xvcjogJyNEREEwREQnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn6Ie05YWs5YWaJywKICAgICAgICB2YWx1ZTogOCwKICAgICAgICBwZXJjZW50YWdlOiAzLAogICAgICAgIGNvbG9yOiAnIzk4RDhDOCcKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfkuZ3kuInlrabnpL4nLAogICAgICAgIHZhbHVlOiA3LAogICAgICAgIHBlcmNlbnRhZ2U6IDMsCiAgICAgICAgY29sb3I6ICcjRjdEQzZGJwogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+WPsOebnycsCiAgICAgICAgdmFsdWU6IDYsCiAgICAgICAgcGVyY2VudGFnZTogMiwKICAgICAgICBjb2xvcjogJyNCQjhGQ0UnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5peg5YWa5rS+5Lq65aOrJywKICAgICAgICB2YWx1ZTogNSwKICAgICAgICBwZXJjZW50YWdlOiAyLAogICAgICAgIGNvbG9yOiAnIzg1QzFFOScKICAgICAgfV0sCiAgICAgIC8vIOeVjOWIq+WIhuaekOaVsOaNrgogICAgICBzZWN0b3JBbmFseXNpc0RhdGE6IFt7CiAgICAgICAgbmFtZTogJ+e7j+a1jueVjCcsCiAgICAgICAgdmFsdWU6IDMyCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5pWZ6IKy55WMJywKICAgICAgICB2YWx1ZTogMTUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnp5HmioDnlYwnLAogICAgICAgIHZhbHVlOiAxNAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+W3peWVhueVjCcsCiAgICAgICAgdmFsdWU6IDEzCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5Yy76I2v5Y2r55Sf55WMJywKICAgICAgICB2YWx1ZTogMTIKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnpL7kvJrnp5HlrabnlYwnLAogICAgICAgIHZhbHVlOiAxMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+W3peS8micsCiAgICAgICAgdmFsdWU6IDgKICAgICAgfSwgewogICAgICAgIG5hbWU6ICflhbHpnZLlm6InLAogICAgICAgIHZhbHVlOiA3CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5aaH6IGUJywKICAgICAgICB2YWx1ZTogNgogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+enkeWNjycsCiAgICAgICAgdmFsdWU6IDUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICflj7DogZQnLAogICAgICAgIHZhbHVlOiA3CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5L6o6IGUJywKICAgICAgICB2YWx1ZTogMwogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+aWh+WMluiJuuacr+eVjCcsCiAgICAgICAgdmFsdWU6IDI0CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5L2T6IKy55WMJywKICAgICAgICB2YWx1ZTogMTYKICAgICAgfSwgewogICAgICAgIG5hbWU6ICflsJHmlbDmsJHml4/nlYwnLAogICAgICAgIHZhbHVlOiAyMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+Wul+aVmeeVjCcsCiAgICAgICAgdmFsdWU6IDI3CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn54m56YKA5Lq65aOrJywKICAgICAgICB2YWx1ZTogMjEKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfmuK/mvrPlj7DkvqgnLAogICAgICAgIHZhbHVlOiA1CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5a+55aSW5Y+L5aW955WMJywKICAgICAgICB2YWx1ZTogMTkKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnpL7kvJrnpo/liKnlkoznpL7kvJrkv53pmpznlYwnLAogICAgICAgIHZhbHVlOiAxMgogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+ekvuS8muayu+eQhuWSjOekvuS8mue7hOe7h+eVjCcsCiAgICAgICAgdmFsdWU6IDIxCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5Yy76I2v5Y2r55Sf55WMJywKICAgICAgICB2YWx1ZTogMTIKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnpL7kvJrnp5HlrabnlYwnLAogICAgICAgIHZhbHVlOiAxMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+W3peS8micsCiAgICAgICAgdmFsdWU6IDgKICAgICAgfSwgewogICAgICAgIG5hbWU6ICflhbHpnZLlm6InLAogICAgICAgIHZhbHVlOiA3CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5aaH6IGUJywKICAgICAgICB2YWx1ZTogNgogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+enkeWNjycsCiAgICAgICAgdmFsdWU6IDUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICflj7DogZQnLAogICAgICAgIHZhbHVlOiA3CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5L2T6IKy55WMJywKICAgICAgICB2YWx1ZTogMTYKICAgICAgfSwgewogICAgICAgIG5hbWU6ICflsJHmlbDmsJHml4/nlYwnLAogICAgICAgIHZhbHVlOiAyMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+Wul+aVmeeVjCcsCiAgICAgICAgdmFsdWU6IDI3CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn54m56YKA5Lq65aOrJywKICAgICAgICB2YWx1ZTogMjEKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfmuK/mvrPlj7DkvqgnLAogICAgICAgIHZhbHVlOiA1CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5a+55aSW5Y+L5aW955WMJywKICAgICAgICB2YWx1ZTogMTkKICAgICAgfV0sCiAgICAgIC8vIOiuqOiuuue7hOS6uuWRmOe7n+iuoeaVsOaNrgogICAgICBkaXNjdXNzaW9uR3JvdXBEYXRhOiBbewogICAgICAgIG5hbWU6ICfnrKwx57uEJywKICAgICAgICB2YWx1ZTogNjUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKwy57uEJywKICAgICAgICB2YWx1ZTogNDIKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKwz57uEJywKICAgICAgICB2YWx1ZTogNjMKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKw057uEJywKICAgICAgICB2YWx1ZTogNDUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKw157uEJywKICAgICAgICB2YWx1ZTogNjgKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKw257uEJywKICAgICAgICB2YWx1ZTogMzgKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKw357uEJywKICAgICAgICB2YWx1ZTogNDEKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKw457uEJywKICAgICAgICB2YWx1ZTogMzkKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKw557uEJywKICAgICAgICB2YWx1ZTogNDMKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKwxMOe7hCcsCiAgICAgICAgdmFsdWU6IDU4CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn56ysMTHnu4QnLAogICAgICAgIHZhbHVlOiAzNgogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+esrDEy57uEJywKICAgICAgICB2YWx1ZTogMTUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKwxM+e7hCcsCiAgICAgICAgdmFsdWU6IDU1CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn56ysMTTnu4QnLAogICAgICAgIHZhbHVlOiA0MgogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+esrDE157uEJywKICAgICAgICB2YWx1ZTogNjYKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKwxNue7hCcsCiAgICAgICAgdmFsdWU6IDM1CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn56ysMTfnu4QnLAogICAgICAgIHZhbHVlOiAyOAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+esrDE457uEJywKICAgICAgICB2YWx1ZTogNDAKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfnrKwxOee7hCcsCiAgICAgICAgdmFsdWU6IDQ4CiAgICAgIH1dLAogICAgICAvLyDlubTpvoTmlbDmja4KICAgICAgYWdlQ2hhcnREYXRhOiBbewogICAgICAgIG5hbWU6ICcyOeWygeS7peS4iycsCiAgICAgICAgdmFsdWU6IDIwLAogICAgICAgIHBlcmNlbnRhZ2U6ICc1JScKICAgICAgfSwgewogICAgICAgIG5hbWU6ICczMC0zOeWygScsCiAgICAgICAgdmFsdWU6IDEyNSwKICAgICAgICBwZXJjZW50YWdlOiAnMzAlJwogICAgICB9LCB7CiAgICAgICAgbmFtZTogJzQwLTQ55bKBJywKICAgICAgICB2YWx1ZTogMTY4LAogICAgICAgIHBlcmNlbnRhZ2U6ICc0MCUnCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAnNTAtNTnlsoEnLAogICAgICAgIHZhbHVlOiA4NSwKICAgICAgICBwZXJjZW50YWdlOiAnMjAlJwogICAgICB9LCB7CiAgICAgICAgbmFtZTogJzYw5bKB5Lul5LiKJywKICAgICAgICB2YWx1ZTogMjAsCiAgICAgICAgcGVyY2VudGFnZTogJzUlJwogICAgICB9XSwKICAgICAgYWdlQ2hhcnROYW1lOiAn5bm06b6E5Y2g5q+UJywKICAgICAgbWVtYmVyVG90YWxOdW06IDQxOCwKICAgICAgc3RhbmRpbmdNZW1iZXJUb3RhbE51bTogNDIKICAgIH07CiAgfSwKCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOW9k+WJjeaYvuekuueahOWcsOWMuuWQjeensAogICAgY3VycmVudEFyZWFEaXNwbGF5KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZERpc3RyaWN0KSB7CiAgICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0ZWREaXN0cmljdDsKICAgICAgfQoKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0ZWRDaXR5OwogICAgfQoKICB9LAoKICBtb3VudGVkKCkgewogICAgdGhpcy5pbml0U2NyZWVuKCk7CiAgICB0aGlzLnVwZGF0ZVRpbWUoKTsKICAgIHRoaXMudGltZUludGVydmFsID0gc2V0SW50ZXJ2YWwodGhpcy51cGRhdGVUaW1lLCAxMDAwKTsKICB9LAoKICBiZWZvcmVEZXN0cm95KCkgewogICAgaWYgKHRoaXMudGltZUludGVydmFsKSB7CiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50aW1lSW50ZXJ2YWwpOwogICAgfQogIH0sCgogIG1ldGhvZHM6IHsKICAgIGluaXRTY3JlZW4oKSB7CiAgICAgIGNvbnN0IHsKICAgICAgICBjYWxjUmF0ZSwKICAgICAgICB3aW5kb3dEcmF3CiAgICAgIH0gPSB1c2VJbmRleCh0aGlzLiRyZWZzLmJpZ1NjcmVlbik7CiAgICAgIGNhbGNSYXRlKCk7CiAgICAgIHdpbmRvd0RyYXcoKTsKICAgIH0sCgogICAgdXBkYXRlVGltZSgpIHsKICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsKICAgICAgdGhpcy5jdXJyZW50VGltZSA9IG5vdy50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7CiAgICAgICAgeWVhcjogJ251bWVyaWMnLAogICAgICAgIG1vbnRoOiAnMi1kaWdpdCcsCiAgICAgICAgZGF5OiAnMi1kaWdpdCcsCiAgICAgICAgaG91cjogJzItZGlnaXQnLAogICAgICAgIG1pbnV0ZTogJzItZGlnaXQnLAogICAgICAgIHNlY29uZDogJzItZGlnaXQnCiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDov5Tlm57pppbpobUKICAgIGdvSG9tZSgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICcvaG9tZUJveCcKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOmAieaLqeWfjuW4ggogICAgc2VsZWN0Q2l0eShjaXR5KSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRDaXR5ID0gY2l0eS5uYW1lOwogICAgICB0aGlzLnNlbGVjdGVkRGlzdHJpY3QgPSAnJzsgLy8g5riF56m65Yy65Y6/6YCJ5oupCgogICAgICB0aGlzLnNob3dBcmVhUG9wb3ZlciA9IGZhbHNlOyAvLyDov5nph4zlj6/ku6Xmt7vliqDliIfmjaLln47luILlkI7nmoTmlbDmja7mm7TmlrDpgLvovpEKCiAgICAgIGNvbnNvbGUubG9nKCfpgInmi6nkuobln47luII6JywgY2l0eSk7CiAgICB9LAoKICAgIC8vIOmAieaLqeWMuuWOvwogICAgc2VsZWN0RGlzdHJpY3QoZGlzdHJpY3QpIHsKICAgICAgdGhpcy5zZWxlY3RlZERpc3RyaWN0ID0gZGlzdHJpY3QubmFtZTsKICAgICAgdGhpcy5zaG93QXJlYVBvcG92ZXIgPSBmYWxzZTsgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5YiH5o2i5Yy65Y6/5ZCO55qE5pWw5o2u5pu05paw6YC76L6RCgogICAgICBjb25zb2xlLmxvZygn6YCJ5oup5LqG5Yy65Y6/OicsIGRpc3RyaWN0KTsKICAgIH0KCiAgfQp9Ow=="}, {"version": 3, "mappings": "AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC,cADA;IAEAC,QAFA;IAGAC,UAHA;IAIAC,QAJA;IAKAC,kBALA;IAMAC;EANA,CAFA;;EAUAC;IACA;MACAC,eADA;MAEA;MACAC,sBAHA;MAIAC,iBAJA;MAKAC,kBALA;MAMAC,oBANA;MAOAC,WACA;QAAAd;QAAAe;MAAA,CADA,EAEA;QAAAf;QAAAe;MAAA,CAFA,EAGA;QAAAf;QAAAe;MAAA,CAHA,EAIA;QAAAf;QAAAe;MAAA,CAJA,EAKA;QAAAf;QAAAe;MAAA,CALA,EAMA;QAAAf;QAAAe;MAAA,CANA,CAPA;MAeAC,eACA;QAAAhB;QAAAe;MAAA,CADA,EAEA;QAAAf;QAAAe;MAAA,CAFA,EAGA;QAAAf;QAAAe;MAAA,CAHA,EAIA;QAAAf;QAAAe;MAAA,CAJA,EAKA;QAAAf;QAAAe;MAAA,CALA,EAMA;QAAAf;QAAAe;MAAA,CANA,EAOA;QAAAf;QAAAe;MAAA,CAPA,EAQA;QAAAf;QAAAe;MAAA,CARA,EASA;QAAAf;QAAAe;MAAA,CATA,EAUA;QAAAf;QAAAe;MAAA,CAVA,CAfA;MA2BA;MACAE,gBACA;QAAAjB;QAAAkB;MAAA,CADA,EAEA;QAAAlB;QAAAkB;MAAA,CAFA,EAGA;QAAAlB;QAAAkB;MAAA,CAHA,EAIA;QAAAlB;QAAAkB;MAAA,CAJA,EAKA;QAAAlB;QAAAkB;MAAA,CALA,EAMA;QAAAlB;QAAAkB;MAAA,CANA,CA5BA;MAoCA;MACAC,YACA;QAAAnB;QAAAkB;QAAAE;QAAAC;MAAA,CADA,EAEA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CAFA,EAGA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CAHA,EAIA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CAJA,EAKA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CALA,EAMA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CANA,EAOA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CAPA,EAQA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CARA,EASA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CATA,EAUA;QAAArB;QAAAkB;QAAAE;QAAAC;MAAA,CAVA,CArCA;MAiDA;MACAC,qBACA;QAAAtB;QAAAkB;MAAA,CADA,EAEA;QAAAlB;QAAAkB;MAAA,CAFA,EAGA;QAAAlB;QAAAkB;MAAA,CAHA,EAIA;QAAAlB;QAAAkB;MAAA,CAJA,EAKA;QAAAlB;QAAAkB;MAAA,CALA,EAMA;QAAAlB;QAAAkB;MAAA,CANA,EAOA;QAAAlB;QAAAkB;MAAA,CAPA,EAQA;QAAAlB;QAAAkB;MAAA,CARA,EASA;QAAAlB;QAAAkB;MAAA,CATA,EAUA;QAAAlB;QAAAkB;MAAA,CAVA,EAWA;QAAAlB;QAAAkB;MAAA,CAXA,EAYA;QAAAlB;QAAAkB;MAAA,CAZA,EAaA;QAAAlB;QAAAkB;MAAA,CAbA,EAcA;QAAAlB;QAAAkB;MAAA,CAdA,EAeA;QAAAlB;QAAAkB;MAAA,CAfA,EAgBA;QAAAlB;QAAAkB;MAAA,CAhBA,EAiBA;QAAAlB;QAAAkB;MAAA,CAjBA,EAkBA;QAAAlB;QAAAkB;MAAA,CAlBA,EAmBA;QAAAlB;QAAAkB;MAAA,CAnBA,EAoBA;QAAAlB;QAAAkB;MAAA,CApBA,EAqBA;QAAAlB;QAAAkB;MAAA,CArBA,EAsBA;QAAAlB;QAAAkB;MAAA,CAtBA,EAuBA;QAAAlB;QAAAkB;MAAA,CAvBA,EAwBA;QAAAlB;QAAAkB;MAAA,CAxBA,EAyBA;QAAAlB;QAAAkB;MAAA,CAzBA,EA0BA;QAAAlB;QAAAkB;MAAA,CA1BA,EA2BA;QAAAlB;QAAAkB;MAAA,CA3BA,EA4BA;QAAAlB;QAAAkB;MAAA,CA5BA,EA6BA;QAAAlB;QAAAkB;MAAA,CA7BA,EA8BA;QAAAlB;QAAAkB;MAAA,CA9BA,EA+BA;QAAAlB;QAAAkB;MAAA,CA/BA,EAgCA;QAAAlB;QAAAkB;MAAA,CAhCA,EAiCA;QAAAlB;QAAAkB;MAAA,CAjCA,EAkCA;QAAAlB;QAAAkB;MAAA,CAlCA,CAlDA;MAsFA;MACAK,sBACA;QAAAvB;QAAAkB;MAAA,CADA,EAEA;QAAAlB;QAAAkB;MAAA,CAFA,EAGA;QAAAlB;QAAAkB;MAAA,CAHA,EAIA;QAAAlB;QAAAkB;MAAA,CAJA,EAKA;QAAAlB;QAAAkB;MAAA,CALA,EAMA;QAAAlB;QAAAkB;MAAA,CANA,EAOA;QAAAlB;QAAAkB;MAAA,CAPA,EAQA;QAAAlB;QAAAkB;MAAA,CARA,EASA;QAAAlB;QAAAkB;MAAA,CATA,EAUA;QAAAlB;QAAAkB;MAAA,CAVA,EAWA;QAAAlB;QAAAkB;MAAA,CAXA,EAYA;QAAAlB;QAAAkB;MAAA,CAZA,EAaA;QAAAlB;QAAAkB;MAAA,CAbA,EAcA;QAAAlB;QAAAkB;MAAA,CAdA,EAeA;QAAAlB;QAAAkB;MAAA,CAfA,EAgBA;QAAAlB;QAAAkB;MAAA,CAhBA,EAiBA;QAAAlB;QAAAkB;MAAA,CAjBA,EAkBA;QAAAlB;QAAAkB;MAAA,CAlBA,EAmBA;QAAAlB;QAAAkB;MAAA,CAnBA,CAvFA;MA4GA;MACAM,eACA;QAAAxB;QAAAkB;QAAAE;MAAA,CADA,EAEA;QAAApB;QAAAkB;QAAAE;MAAA,CAFA,EAGA;QAAApB;QAAAkB;QAAAE;MAAA,CAHA,EAIA;QAAApB;QAAAkB;QAAAE;MAAA,CAJA,EAKA;QAAApB;QAAAkB;QAAAE;MAAA,CALA,CA7GA;MAoHAK,oBApHA;MAqHAC,mBArHA;MAsHAC;IAtHA;EAwHA,CAnIA;;EAoIAC;IACA;IACAC;MACA;QACA;MACA;;MACA;IACA;;EAPA,CApIA;;EA6IAC;IACA;IACA;IACA;EACA,CAjJA;;EAkJAC;IACA;MACAC;IACA;EACA,CAtJA;;EAuJAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBA;IACAC;MACA;QAAAC;MAAA;IACA,CApBA;;IAqBA;IACAC;MACA;MACA,2BAFA,CAEA;;MACA,6BAHA,CAIA;;MACAC;IACA,CA5BA;;IA6BA;IACAC;MACA;MACA,6BAFA,CAGA;;MACAD;IACA;;EAnCA;AAvJA", "names": ["name", "components", "BarScrollChart", "<PERSON><PERSON><PERSON>", "PieChart3D", "<PERSON><PERSON><PERSON>", "HorizontalBarChart", "GenderRatioChart", "data", "currentTime", "showAreaPopover", "activeTab", "selectedCity", "selectedDistrict", "cityList", "code", "districtList", "educationData", "value", "partyData", "percentage", "color", "sectorAnalysisData", "discussionGroupData", "ageChartData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "memberTotalNum", "standingMemberTotalNum", "computed", "currentAreaDisplay", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "goHome", "path", "selectCity", "console", "selectDistrict"], "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sources": ["committeeStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <el-popover placement=\"bottom\" width=\"300\" trigger=\"click\" popper-class=\"area-popover\"\r\n            v-model=\"showAreaPopover\">\r\n            <div class=\"area-selector\">\r\n              <div class=\"area-tabs\">\r\n                <div class=\"area-tab\" :class=\"{ 'active': activeTab === 'city' }\" @click=\"activeTab = 'city'\">\r\n                  城市\r\n                </div>\r\n                <div class=\"area-tab\" :class=\"{ 'active': activeTab === 'district' }\" @click=\"activeTab = 'district'\">\r\n                  区县\r\n                </div>\r\n              </div>\r\n              <div class=\"area-content\">\r\n                <!-- 城市选择 -->\r\n                <div v-if=\"activeTab === 'city'\" class=\"area-list\">\r\n                  <div class=\"area-item\" v-for=\"city in cityList\" :key=\"city.code\"\r\n                    :class=\"{ 'selected': city.name === selectedCity }\" @click=\"selectCity(city)\">\r\n                    {{ city.name }}\r\n                  </div>\r\n                </div>\r\n                <!-- 区县选择 -->\r\n                <div v-if=\"activeTab === 'district'\" class=\"area-list\">\r\n                  <div class=\"area-item\" v-for=\"district in districtList\" :key=\"district.code\"\r\n                    :class=\"{ 'selected': district.name === selectedDistrict }\" @click=\"selectDistrict(district)\">\r\n                    {{ district.name }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"header-btn area-select-btn\" slot=\"reference\">\r\n              <span>{{ currentAreaDisplay }}</span>\r\n              <i class=\"dropdown-icon\" :class=\"{ 'active': showAreaPopover }\">▼</i>\r\n            </div>\r\n          </el-popover>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #02FBFB;\">{{ memberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_member.png\" class=\"count-img\">\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #F5E74F;\">{{ standingMemberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_standingMember.png\" class=\"count-img\">\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" :male-ratio=\"100\" :female-ratio=\"60\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" :chart-data=\"ageChartData\" :name=\"ageChartName\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      activeTab: 'city',\r\n      selectedCity: '青岛',\r\n      selectedDistrict: '',\r\n      cityList: [\r\n        { name: '青岛', code: 'qingdao' },\r\n        { name: '济南', code: 'jinan' },\r\n        { name: '烟台', code: 'yantai' },\r\n        { name: '潍坊', code: 'weifang' },\r\n        { name: '临沂', code: 'linyi' },\r\n        { name: '淄博', code: 'zibo' }\r\n      ],\r\n      districtList: [\r\n        { name: '市南区', code: 'shinan' },\r\n        { name: '市北区', code: 'shibei' },\r\n        { name: '李沧区', code: 'licang' },\r\n        { name: '崂山区', code: 'laoshan' },\r\n        { name: '城阳区', code: 'chengyang' },\r\n        { name: '即墨区', code: 'jimo' },\r\n        { name: '胶州市', code: 'jiaozhou' },\r\n        { name: '平度市', code: 'pingdu' },\r\n        { name: '莱西市', code: 'laixi' },\r\n        { name: '西海岸新区', code: 'xihaian' }\r\n      ],\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '研究生', value: 84 },\r\n        { name: '本科', value: 165 },\r\n        { name: '大专', value: 500 },\r\n        { name: '高中', value: 200 },\r\n        { name: '职高', value: 160 },\r\n        { name: '初中', value: 90 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [\r\n        { name: '第1组', value: 65 },\r\n        { name: '第2组', value: 42 },\r\n        { name: '第3组', value: 63 },\r\n        { name: '第4组', value: 45 },\r\n        { name: '第5组', value: 68 },\r\n        { name: '第6组', value: 38 },\r\n        { name: '第7组', value: 41 },\r\n        { name: '第8组', value: 39 },\r\n        { name: '第9组', value: 43 },\r\n        { name: '第10组', value: 58 },\r\n        { name: '第11组', value: 36 },\r\n        { name: '第12组', value: 15 },\r\n        { name: '第13组', value: 55 },\r\n        { name: '第14组', value: 42 },\r\n        { name: '第15组', value: 66 },\r\n        { name: '第16组', value: 35 },\r\n        { name: '第17组', value: 28 },\r\n        { name: '第18组', value: 40 },\r\n        { name: '第19组', value: 48 }\r\n      ],\r\n      // 年龄数据\r\n      ageChartData: [\r\n        { name: '29岁以下', value: 20, percentage: '5%' },\r\n        { name: '30-39岁', value: 125, percentage: '30%' },\r\n        { name: '40-49岁', value: 168, percentage: '40%' },\r\n        { name: '50-59岁', value: 85, percentage: '20%' },\r\n        { name: '60岁以上', value: 20, percentage: '5%' }\r\n      ],\r\n      ageChartName: '年龄占比',\r\n      memberTotalNum: 418,\r\n      standingMemberTotalNum: 42\r\n    }\r\n  },\r\n  computed: {\r\n    // 当前显示的地区名称\r\n    currentAreaDisplay () {\r\n      if (this.selectedDistrict) {\r\n        return this.selectedDistrict\r\n      }\r\n      return this.selectedCity\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 选择城市\r\n    selectCity (city) {\r\n      this.selectedCity = city.name\r\n      this.selectedDistrict = '' // 清空区县选择\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换城市后的数据更新逻辑\r\n      console.log('选择了城市:', city)\r\n    },\r\n    // 选择区县\r\n    selectDistrict (district) {\r\n      this.selectedDistrict = district.name\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换区县后的数据更新逻辑\r\n      console.log('选择了区县:', district)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-weight: 500;\r\n              font-size: 32px;\r\n            }\r\n\r\n            .count-img {\r\n              width: 118px;\r\n              margin-top: -10px;\r\n              margin-bottom: 10px;\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// el-popover 自定义样式\r\n.area-popover {\r\n  background: rgba(7, 52, 95, 0.95) !important;\r\n  border: 1px solid rgba(0, 181, 254, 0.5) !important;\r\n  border-radius: 8px !important;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\r\n\r\n  .area-selector {\r\n    .area-tabs {\r\n      display: flex;\r\n      border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n      margin-bottom: 12px;\r\n\r\n      .area-tab {\r\n        flex: 1;\r\n        padding: 12px 16px;\r\n        text-align: center;\r\n        color: rgba(255, 255, 255, 0.7);\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border-bottom: 2px solid transparent;\r\n\r\n        &:hover {\r\n          color: #1FC6FF;\r\n        }\r\n\r\n        &.active {\r\n          color: #1FC6FF;\r\n          border-bottom-color: #1FC6FF;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n\r\n    .area-content {\r\n      max-height: 200px;\r\n      overflow-y: auto;\r\n\r\n      .area-list {\r\n        display: grid;\r\n        grid-template-columns: repeat(2, 1fr);\r\n        gap: 8px;\r\n\r\n        .area-item {\r\n          padding: 10px 12px;\r\n          color: #FFFFFF;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          border-radius: 4px;\r\n          text-align: center;\r\n          border: 1px solid transparent;\r\n\r\n          &:hover {\r\n            background: rgba(0, 181, 254, 0.2);\r\n            color: #1FC6FF;\r\n            border-color: rgba(0, 181, 254, 0.3);\r\n          }\r\n\r\n          &.selected {\r\n            background: rgba(0, 181, 254, 0.4);\r\n            color: #1FC6FF;\r\n            font-weight: bold;\r\n            border-color: #1FC6FF;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 自定义滚动条\r\n      &::-webkit-scrollbar {\r\n        width: 4px;\r\n      }\r\n\r\n      &::-webkit-scrollbar-track {\r\n        background: rgba(255, 255, 255, 0.1);\r\n        border-radius: 2px;\r\n      }\r\n\r\n      &::-webkit-scrollbar-thumb {\r\n        background: rgba(0, 181, 254, 0.5);\r\n        border-radius: 2px;\r\n\r\n        &:hover {\r\n          background: rgba(0, 181, 254, 0.7);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}