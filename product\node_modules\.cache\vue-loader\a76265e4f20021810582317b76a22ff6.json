{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1755654714324}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: '<PERSON><PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#26C6DA', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index) {\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'vertical',\n          right: this.id === 'proposal-statistics' ? '0%' : '5%',\n          top: 'center',\n          itemWidth: 5,\n          itemHeight: 5,\n          icon: 'circle',\n          itemGap: this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            return `${name}  ${item ? item.value : ''}%`\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['60%', '85%'] : ['55%', '80%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用你的大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['94%', '95%'] : ['88%', '89%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'proposal-statistics' ? '12%' : '17%',\n            top: this.id === 'proposal-statistics' ? '23%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'proposal-statistics' ? 40 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}