{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1755661503393}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUGllQ2hhcnQnLAogIHByb3BzOiB7CiAgICBpZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgbmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2hhcnREYXRhOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0KICB9LAogIGRhdGEgKCkgewogICAgcmV0dXJuIHsKICAgICAgY2hhcnQ6IG51bGwsCiAgICAgIGF1dG9IaWdobGlnaHRUaW1lcjogbnVsbCwgLy8g6Ieq5Yqo6auY5Lqu5a6a5pe25ZmoCiAgICAgIGN1cnJlbnRIaWdobGlnaHRJbmRleDogLTEsIC8vIOW9k+WJjemrmOS6rueahOaVsOaNrumhuee0ouW8lQogICAgICAvLyDpooTlrprkuYnnmoTpopzoibLmlbDnu4TvvIzmjInpobrluo/liIbphY3nu5nmlbDmja7pobkKICAgICAgY29sb3JzOiBbCiAgICAgICAgJyM0RkMzRjcnLCAnIzI2QzZEQScsICcjNjZCQjZBJywgJyNGRkE3MjYnLAogICAgICAgICcjRkY3MDQzJywgJyNBQjQ3QkMnLCAnIzVDNkJDMCcsICcjNDJBNUY1JywKICAgICAgICAnI0ZGQ0EyOCcsICcjNENBRjUwJywgJyNFRjUzNTAnLCAnIzhENkU2MycsCiAgICAgICAgJyM5QzI3QjAnLCAnIzNGNTFCNScsICcjMjE5NkYzJywgJyMwMEJDRDQnLAogICAgICAgICcjRkY5ODAwJywgJyM3OTU1NDgnLCAnIzYwN0Q4QicsICcjRTkxRTYzJwogICAgICBdCiAgICB9CiAgfSwKICBtb3VudGVkICgpIHsKICAgIHRoaXMuaW5pdENoYXJ0KCkKICB9LAogIGJlZm9yZURlc3Ryb3kgKCkgewogICAgLy8g5riF6Zmk6Ieq5Yqo6auY5Lqu5a6a5pe25ZmoCiAgICBpZiAodGhpcy5hdXRvSGlnaGxpZ2h0VGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmF1dG9IaWdobGlnaHRUaW1lcikKICAgIH0KICAgIGlmICh0aGlzLmNoYXJ0KSB7CiAgICAgIHRoaXMuY2hhcnQuZGlzcG9zZSgpCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRDb2xvciAoaW5kZXgpIHsKICAgICAgcmV0dXJuIHRoaXMuY29sb3JzW2luZGV4ICUgdGhpcy5jb2xvcnMubGVuZ3RoXQogICAgfSwKICAgIGluaXRDaGFydCAoKSB7CiAgICAgIGNvbnN0IGNoYXJ0Q29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQodGhpcy5pZCkKICAgICAgaWYgKCFjaGFydENvbnRhaW5lcikgcmV0dXJuCiAgICAgIHRoaXMuY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnRDb250YWluZXIpCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsCiAgICAgICAgICBmb3JtYXR0ZXI6ICd7YX0gPGJyLz57Yn06IHtjfSAoe2R9JSknLAogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjgpJywKICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzAwZDRmZicsCiAgICAgICAgICBib3JkZXJXaWR0aDogMSwKICAgICAgICAgIHRleHRTdHlsZTogewogICAgICAgICAgICBjb2xvcjogJyNmZmYnCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgIG9yaWVudDogJ3ZlcnRpY2FsJywKICAgICAgICAgIHJpZ2h0OiB0aGlzLmlkID09PSAncHJvcG9zYWwtc3RhdGlzdGljcycgPyAnMCUnIDogJzUlJywKICAgICAgICAgIHRvcDogJ2NlbnRlcicsCiAgICAgICAgICBpdGVtV2lkdGg6IDUsCiAgICAgICAgICBpdGVtSGVpZ2h0OiA1LAogICAgICAgICAgaWNvbjogJ2NpcmNsZScsCiAgICAgICAgICBpdGVtR2FwOiB0aGlzLmlkID09PSAncHJvcG9zYWwtc3RhdGlzdGljcycgPyAxMiA6IDI1LAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsCiAgICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgICAgZm9udEZhbWlseTogJ01pY3Jvc29mdCBZYUhlaScKICAgICAgICAgIH0sCiAgICAgICAgICBmb3JtYXR0ZXI6IChuYW1lKSA9PiB7CiAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmNoYXJ0RGF0YS5maW5kKGQgPT4gZC5uYW1lID09PSBuYW1lKQogICAgICAgICAgICByZXR1cm4gYCR7bmFtZX0gICR7aXRlbSA/IGl0ZW0udmFsdWUgOiAnJ30lYAogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSwKICAgICAgICAgICAgdHlwZTogJ3BpZScsCiAgICAgICAgICAgIHJhZGl1czogdGhpcy5pZCA9PT0gJ3Byb3Bvc2FsLXN0YXRpc3RpY3MnID8gWyc2MCUnLCAnODUlJ10gOiBbJzU1JScsICc4MCUnXSwKICAgICAgICAgICAgY2VudGVyOiB0aGlzLmlkID09PSAncHJvcG9zYWwtc3RhdGlzdGljcycgPyBbJzIyJScsICc1MCUnXSA6IFsnMzAlJywgJzUwJSddLAogICAgICAgICAgICBhdm9pZExhYmVsT3ZlcmxhcDogZmFsc2UsCiAgICAgICAgICAgIGVtcGhhc2lzOiB7CiAgICAgICAgICAgICAgc2NhbGU6IHRydWUsCiAgICAgICAgICAgICAgc2NhbGVTaXplOiAxMAogICAgICAgICAgICB9LAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgICAgcG9zaXRpb246ICdjZW50ZXInLAogICAgICAgICAgICAgIGZvbnRTaXplOiAxNiwKICAgICAgICAgICAgICBjb2xvcjogJyNmZmYnLAogICAgICAgICAgICAgIGZvcm1hdHRlcjogdGhpcy5uYW1lCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGxhYmVsTGluZTogewogICAgICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAzLAogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzA3MzQ1RicgLy8g55So5L2g55qE5aSn5bGP6IOM5pmv6ImyCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGRhdGE6IHRoaXMuY2hhcnREYXRhLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7CiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsCiAgICAgICAgICAgICAgbmFtZTogaXRlbS5uYW1lLAogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogdGhpcy5nZXRDb2xvcihpbmRleCkgfQogICAgICAgICAgICB9KSkKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHR5cGU6ICdwaWUnLAogICAgICAgICAgICByYWRpdXM6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/IFsnOTQlJywgJzk1JSddIDogWyc4OCUnLCAnODklJ10sCiAgICAgICAgICAgIGNlbnRlcjogdGhpcy5pZCA9PT0gJ3Byb3Bvc2FsLXN0YXRpc3RpY3MnID8gWycyMiUnLCAnNTAlJ10gOiBbJzMwJScsICc1MCUnXSwKICAgICAgICAgICAgZGF0YTogWwogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAxMDAsCiAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMmY2ODlhJwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBncmFwaGljOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHR5cGU6ICdjaXJjbGUnLAogICAgICAgICAgICBsZWZ0OiB0aGlzLmlkID09PSAncHJvcG9zYWwtc3RhdGlzdGljcycgPyAnMTIlJyA6ICcxNyUnLAogICAgICAgICAgICB0b3A6IHRoaXMuaWQgPT09ICdwcm9wb3NhbC1zdGF0aXN0aWNzJyA/ICcyMyUnIDogJzI2JScsCiAgICAgICAgICAgIHNoYXBlOiB7CiAgICAgICAgICAgICAgY3g6IDAsCiAgICAgICAgICAgICAgY3k6IDAsCiAgICAgICAgICAgICAgcjogdGhpcy5pZCA9PT0gJ3Byb3Bvc2FsLXN0YXRpc3RpY3MnID8gNDAgOiA1MAogICAgICAgICAgICB9LAogICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgIGZpbGw6ICdub25lJywKICAgICAgICAgICAgICBsaW5lV2lkdGg6IDMsCiAgICAgICAgICAgICAgc3Ryb2tlOiB7CiAgICAgICAgICAgICAgICB0eXBlOiAnbGluZWFyJywKICAgICAgICAgICAgICAgIHg6IDAsCiAgICAgICAgICAgICAgICB5OiAwLAogICAgICAgICAgICAgICAgeDI6IDAsCiAgICAgICAgICAgICAgICB5MjogMSwKICAgICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFsKICAgICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAnIzIzRTFGRicgfSwKICAgICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgzNSwyMjUsMjU1LDApJyB9CiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LAogICAgICAgICAgICB6OiAxMCwKICAgICAgICAgICAgc2lsZW50OiB0cnVlLAogICAgICAgICAgICBwb3NpdGlvbjogWzAsIDBdCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9CiAgICAgIHRoaXMuY2hhcnQuc2V0T3B0aW9uKG9wdGlvbikKICAgICAgLy8g55uR5ZCs56qX5Y+j5aSn5bCP5Y+Y5YyWCiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCAoKSA9PiB7CiAgICAgICAgaWYgKHRoaXMuY2hhcnQpIHsKICAgICAgICAgIHRoaXMuY2hhcnQucmVzaXplKCkKICAgICAgICB9CiAgICAgIH0pCgogICAgICAvLyDlkK/liqjoh6rliqjpq5jkuq7mlYjmnpwKICAgICAgdGhpcy5zdGFydEF1dG9IaWdobGlnaHQoKQoKICAgICAgLy8g5re75Yqg6byg5qCH5LqL5Lu255uR5ZCsCiAgICAgIHRoaXMuY2hhcnQub24oJ21vdXNlb3ZlcicsICgpID0+IHsKICAgICAgICB0aGlzLnN0b3BBdXRvSGlnaGxpZ2h0KCkKICAgICAgfSkKCiAgICAgIHRoaXMuY2hhcnQub24oJ21vdXNlb3V0JywgKCkgPT4gewogICAgICAgIHRoaXMuc3RhcnRBdXRvSGlnaGxpZ2h0KCkKICAgICAgfSkKICAgIH0sCgogICAgLy8g5byA5aeL6Ieq5Yqo6auY5Lqu5pWI5p6cCiAgICBzdGFydEF1dG9IaWdobGlnaHQgKCkgewogICAgICBpZiAodGhpcy5jaGFydERhdGEubGVuZ3RoID09PSAwKSByZXR1cm4KCiAgICAgIHRoaXMuYXV0b0hpZ2hsaWdodFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIC8vIOWPlua2iOW9k+WJjemrmOS6ruWSjHRvb2x0aXAKICAgICAgICBpZiAodGhpcy5jdXJyZW50SGlnaGxpZ2h0SW5kZXggPj0gMCkgewogICAgICAgICAgdGhpcy5jaGFydC5kaXNwYXRjaEFjdGlvbih7CiAgICAgICAgICAgIHR5cGU6ICdkb3ducGxheScsCiAgICAgICAgICAgIHNlcmllc0luZGV4OiAwLAogICAgICAgICAgICBkYXRhSW5kZXg6IHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4CiAgICAgICAgICB9KQogICAgICAgICAgLy8g6ZqQ6JePdG9vbHRpcAogICAgICAgICAgdGhpcy5jaGFydC5kaXNwYXRjaEFjdGlvbih7CiAgICAgICAgICAgIHR5cGU6ICdoaWRlVGlwJwogICAgICAgICAgfSkKICAgICAgICB9CgogICAgICAgIC8vIOmrmOS6ruS4i+S4gOS4quaVsOaNrumhuQogICAgICAgIHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4ID0gKHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4ICsgMSkgJSB0aGlzLmNoYXJ0RGF0YS5sZW5ndGgKICAgICAgICB0aGlzLmNoYXJ0LmRpc3BhdGNoQWN0aW9uKHsKICAgICAgICAgIHR5cGU6ICdoaWdobGlnaHQnLAogICAgICAgICAgc2VyaWVzSW5kZXg6IDAsCiAgICAgICAgICBkYXRhSW5kZXg6IHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4CiAgICAgICAgfSkKCiAgICAgICAgLy8g5pi+56S6dG9vbHRpcAogICAgICAgIHRoaXMuY2hhcnQuZGlzcGF0Y2hBY3Rpb24oewogICAgICAgICAgdHlwZTogJ3Nob3dUaXAnLAogICAgICAgICAgc2VyaWVzSW5kZXg6IDAsCiAgICAgICAgICBkYXRhSW5kZXg6IHRoaXMuY3VycmVudEhpZ2hsaWdodEluZGV4CiAgICAgICAgfSkKICAgICAgfSwgMjAwMCkgLy8g5q+PMuenkuWIh+aNouS4gOasoQogICAgfSwKCiAgICAvLyDlgZzmraLoh6rliqjpq5jkuq7mlYjmnpwKICAgIHN0b3BBdXRvSGlnaGxpZ2h0ICgpIHsKICAgICAgaWYgKHRoaXMuYXV0b0hpZ2hsaWdodFRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmF1dG9IaWdobGlnaHRUaW1lcikKICAgICAgICB0aGlzLmF1dG9IaWdobGlnaHRUaW1lciA9IG51bGwKICAgICAgfQogICAgICAvLyDlj5bmtojmiYDmnInpq5jkuq4KICAgICAgaWYgKHRoaXMuY2hhcnQgJiYgdGhpcy5jdXJyZW50SGlnaGxpZ2h0SW5kZXggPj0gMCkgewogICAgICAgIHRoaXMuY2hhcnQuZGlzcGF0Y2hBY3Rpb24oewogICAgICAgICAgdHlwZTogJ2Rvd25wbGF5JywKICAgICAgICAgIHNlcmllc0luZGV4OiAwLAogICAgICAgICAgZGF0YUluZGV4OiB0aGlzLmN1cnJlbnRIaWdobGlnaHRJbmRleAogICAgICAgIH0pCiAgICAgICAgdGhpcy5jdXJyZW50SGlnaGxpZ2h0SW5kZXggPSAtMQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'PieC<PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#26C6DA', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index) {\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'vertical',\n          right: this.id === 'proposal-statistics' ? '0%' : '5%',\n          top: 'center',\n          itemWidth: 5,\n          itemHeight: 5,\n          icon: 'circle',\n          itemGap: this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            return `${name}  ${item ? item.value : ''}%`\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['60%', '85%'] : ['55%', '80%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用你的大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['94%', '95%'] : ['88%', '89%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'proposal-statistics' ? '12%' : '17%',\n            top: this.id === 'proposal-statistics' ? '23%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'proposal-statistics' ? 40 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}