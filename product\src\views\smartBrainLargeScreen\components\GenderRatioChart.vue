<template>
  <div class="gender-ratio-container">
    <div class="ratio-item">
      <div :id="`male-chart-${id}`" class="chart-container"></div>
      <div class="ratio-label">
        <span class="percentage">{{ maleRatio }}%</span>
        <span class="gender-text">男</span>
      </div>
    </div>
    <div class="ratio-item">
      <div :id="`female-chart-${id}`" class="chart-container"></div>
      <div class="ratio-label">
        <span class="percentage">{{ femaleRatio }}%</span>
        <span class="gender-text">女</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'GenderRatioChart',
  props: {
    id: {
      type: String,
      required: true
    },
    maleRatio: {
      type: Number,
      default: 70
    },
    femaleRatio: {
      type: Number,
      default: 30
    }
  },
  data () {
    return {
      maleChart: null,
      femaleChart: null
    }
  },
  mounted () {
    this.initCharts()
  },
  beforeDestroy () {
    if (this.maleChart) {
      this.maleChart.dispose()
    }
    if (this.femaleChart) {
      this.femaleChart.dispose()
    }
  },
  methods: {
    initCharts () {
      this.initMaleChart()
      this.initFemaleChart()
    },

    initMaleChart () {
      const chartContainer = document.getElementById(`male-chart-${this.id}`)
      if (!chartContainer) return

      this.maleChart = echarts.init(chartContainer)

      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距
      const totalTicks = 30
      const activeTicks = Math.round((this.maleRatio / 100) * totalTicks)
      const tickData = []
      const gapData = []

      // 创建刻度和间距
      for (let i = 0; i < totalTicks; i++) {
        // 刻度线
        tickData.push({
          value: 3, // 刻度线的宽度
          itemStyle: {
            color: i < activeTicks ? '#00D4FF' : 'rgba(255, 255, 255, 0.15)',
            borderWidth: 0
          }
        })
        // 间距
        gapData.push({
          value: 5, // 间距的宽度
          itemStyle: {
            color: 'transparent',
            borderWidth: 0
          }
        })
      }

      // 合并刻度和间距数据
      const combinedData = []
      for (let i = 0; i < totalTicks; i++) {
        combinedData.push(tickData[i])
        combinedData.push(gapData[i])
      }

      const option = {
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        series: [
          // 内部背景圆
          {
            type: 'pie',
            radius: ['0%', '62%'],
            center: ['50%', '50%'],
            data: [{
              value: 100,
              itemStyle: {
                color: 'rgba(0, 212, 255, 0.1)',
                borderWidth: 1
              }
            }],
            label: { show: false },
            labelLine: { show: false },
            silent: true
          },
          // 刻度线
          {
            type: 'pie',
            radius: ['75%', '92%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: combinedData,
            label: { show: false },
            labelLine: { show: false },
            silent: true
          },
          // 外层边框圆
          {
            type: 'pie',
            radius: ['98%', '100%'],
            center: ['50%', '50%'],
            data: [{
              value: 100,
              itemStyle: {
                color: '#105379',
                borderWidth: 0
              }
            }],
            label: { show: false },
            labelLine: { show: false },
            silent: true
          }
        ]
      }

      this.maleChart.setOption(option)
    },

    initFemaleChart () {
      const chartContainer = document.getElementById(`female-chart-${this.id}`)
      if (!chartContainer) return

      this.femaleChart = echarts.init(chartContainer)

      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距
      const totalTicks = 30
      const activeTicks = Math.round((this.femaleRatio / 100) * totalTicks)
      const tickData = []
      const gapData = []

      // 创建刻度和间距
      for (let i = 0; i < totalTicks; i++) {
        // 刻度线
        tickData.push({
          value: 3, // 刻度线的宽度
          itemStyle: {
            color: i < activeTicks ? '#FFD700' : 'rgba(255, 255, 255, 0.15)',
            borderWidth: 0
          }
        })
        // 间距
        gapData.push({
          value: 5, // 间距的宽度
          itemStyle: {
            color: 'transparent',
            borderWidth: 0
          }
        })
      }

      // 合并刻度和间距数据
      const combinedData = []
      for (let i = 0; i < totalTicks; i++) {
        combinedData.push(tickData[i])
        combinedData.push(gapData[i])
      }

      const option = {
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        series: [
          // 内部背景圆
          {
            type: 'pie',
            radius: ['0%', '62%'],
            center: ['50%', '50%'],
            data: [{
              value: 100,
              itemStyle: {
                color: 'rgba(255, 215, 0, 0.1)',
                borderWidth: 0
              }
            }],
            label: { show: false },
            labelLine: { show: false },
            silent: true
          },
          // 刻度线
          {
            type: 'pie',
            radius: ['75%', '92%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: combinedData,
            label: { show: false },
            labelLine: { show: false },
            silent: true
          },
          // 外层边框圆
          {
            type: 'pie',
            radius: ['98%', '100%'],
            center: ['50%', '50%'],
            data: [{
              value: 100,
              itemStyle: {
                color: '#105379',
                borderWidth: 0
              }
            }],
            label: { show: false },
            labelLine: { show: false },
            silent: true
          }
        ]
      }

      this.femaleChart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.gender-ratio-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;

  .ratio-item {
    position: relative;
    width: 182px;
    height: 172px;

    .chart-container {
      width: 100%;
      height: 100%;
    }

    .ratio-label {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #fff;
      z-index: 100;
      pointer-events: none;

      .percentage {
        display: block;
        font-size: 28px;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 6px;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
      }

      .gender-text {
        display: block;
        font-size: 16px;
        opacity: 0.9;
        font-weight: 500;
      }
    }
  }
}
</style>
