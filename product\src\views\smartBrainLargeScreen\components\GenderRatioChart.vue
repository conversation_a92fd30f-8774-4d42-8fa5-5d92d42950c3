<template>
  <div class="gender-ratio-container">
    <div class="ratio-item">
      <div :id="`male-chart-${id}`" class="chart-container"></div>
      <div class="ratio-label">
        <span class="percentage">{{ maleRatio }}%</span>
        <span class="gender-text">男</span>
      </div>
    </div>
    <div class="ratio-item">
      <div :id="`female-chart-${id}`" class="chart-container"></div>
      <div class="ratio-label">
        <span class="percentage">{{ femaleRatio }}%</span>
        <span class="gender-text">女</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'GenderRatioChart',
  props: {
    id: {
      type: String,
      required: true
    },
    maleRatio: {
      type: Number,
      default: 70
    },
    femaleRatio: {
      type: Number,
      default: 30
    }
  },
  data () {
    return {
      maleChart: null,
      femaleChart: null
    }
  },
  mounted () {
    this.initCharts()
  },
  beforeDestroy () {
    if (this.maleChart) {
      this.maleChart.dispose()
    }
    if (this.femaleChart) {
      this.femaleChart.dispose()
    }
  },
  methods: {
    initCharts () {
      this.initMaleChart()
      this.initFemaleChart()
    },

    initMaleChart () {
      const chartContainer = document.getElementById(`male-chart-${this.id}`)
      if (!chartContainer) return

      this.maleChart = echarts.init(chartContainer)

      const option = {
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        series: [
          {
            type: 'pie',
            radius: ['70%', '85%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: [
              {
                value: this.maleRatio,
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                    { offset: 0, color: '#00D4FF' },
                    { offset: 0.5, color: '#26C6DA' },
                    { offset: 1, color: '#4FC3F7' }
                  ]),
                  shadowBlur: 10,
                  shadowColor: 'rgba(0, 212, 255, 0.5)'
                }
              },
              {
                value: 100 - this.maleRatio,
                itemStyle: {
                  color: 'rgba(255, 255, 255, 0.08)'
                }
              }
            ],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            silent: true
          },
          // 外圈装饰
          {
            type: 'pie',
            radius: ['88%', '90%'],
            center: ['50%', '50%'],
            data: [
              {
                value: 100,
                itemStyle: {
                  color: 'rgba(0, 212, 255, 0.3)'
                }
              }
            ],
            label: {
              show: false
            },
            silent: true
          }
        ]
      }

      this.maleChart.setOption(option)
    },

    initFemaleChart () {
      const chartContainer = document.getElementById(`female-chart-${this.id}`)
      if (!chartContainer) return

      this.femaleChart = echarts.init(chartContainer)

      const option = {
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        series: [
          {
            type: 'pie',
            radius: ['70%', '85%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: [
              {
                value: this.femaleRatio,
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                    { offset: 0, color: '#FFD700' },
                    { offset: 0.5, color: '#FFCA28' },
                    { offset: 1, color: '#FFA726' }
                  ]),
                  shadowBlur: 10,
                  shadowColor: 'rgba(255, 215, 0, 0.5)'
                }
              },
              {
                value: 100 - this.femaleRatio,
                itemStyle: {
                  color: 'rgba(255, 255, 255, 0.08)'
                }
              }
            ],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            silent: true
          },
          // 外圈装饰
          {
            type: 'pie',
            radius: ['88%', '90%'],
            center: ['50%', '50%'],
            data: [
              {
                value: 100,
                itemStyle: {
                  color: 'rgba(255, 215, 0, 0.3)'
                }
              }
            ],
            label: {
              show: false
            },
            silent: true
          }
        ]
      }

      this.femaleChart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.gender-ratio-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  padding: 20px;

  .ratio-item {
    position: relative;
    width: 140px;
    height: 140px;
    border: 2px dashed rgba(0, 212, 255, 0.6);
    border-radius: 12px;
    padding: 15px;
    background: rgba(0, 212, 255, 0.05);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(0, 212, 255, 0.8);
      background: rgba(0, 212, 255, 0.1);
      transform: scale(1.05);
    }

    &:nth-child(2) {
      border-color: rgba(255, 215, 0, 0.6);
      background: rgba(255, 215, 0, 0.05);

      &:hover {
        border-color: rgba(255, 215, 0, 0.8);
        background: rgba(255, 215, 0, 0.1);
      }
    }

    .chart-container {
      width: 100%;
      height: 100%;
    }

    .ratio-label {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #fff;
      z-index: 10;

      .percentage {
        display: block;
        font-size: 28px;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 6px;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
      }

      .gender-text {
        display: block;
        font-size: 16px;
        opacity: 0.9;
        font-weight: 500;
      }
    }
  }
}
</style>
