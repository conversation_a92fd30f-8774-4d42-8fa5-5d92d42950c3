<template>
  <div class="gender-ratio-container">
    <div class="ratio-item">
      <div :id="`male-chart-${id}`" class="chart-container"></div>
      <div class="ratio-label">
        <span class="percentage">{{ maleRatio }}%</span>
        <span class="gender-text">男</span>
      </div>
    </div>
    <div class="ratio-item">
      <div :id="`female-chart-${id}`" class="chart-container"></div>
      <div class="ratio-label">
        <span class="percentage">{{ femaleRatio }}%</span>
        <span class="gender-text">女</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'GenderRatioChart',
  props: {
    id: {
      type: String,
      required: true
    },
    maleRatio: {
      type: Number,
      default: 70
    },
    femaleRatio: {
      type: Number,
      default: 30
    }
  },
  data () {
    return {
      maleChart: null,
      femaleChart: null
    }
  },
  mounted () {
    this.initCharts()
  },
  beforeDestroy () {
    if (this.maleChart) {
      this.maleChart.dispose()
    }
    if (this.femaleChart) {
      this.femaleChart.dispose()
    }
  },
  methods: {
    initCharts () {
      this.initMaleChart()
      this.initFemaleChart()
    },

    initMaleChart () {
      const chartContainer = document.getElementById(`male-chart-${this.id}`)
      if (!chartContainer) return

      this.maleChart = echarts.init(chartContainer)

      // 创建刻度线数据 - 总共40个刻度
      const totalTicks = 40
      const activeTicks = Math.round((this.maleRatio / 100) * totalTicks)
      const tickData = []

      for (let i = 0; i < totalTicks; i++) {
        tickData.push({
          value: 1,
          itemStyle: {
            color: i < activeTicks ? '#00D4FF' : 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            borderColor: 'transparent'
          }
        })
      }

      const option = {
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        series: [
          {
            type: 'pie',
            radius: ['75%', '85%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: tickData,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            silent: true,
            itemStyle: {
              borderWidth: 2,
              borderColor: 'rgba(7, 52, 95, 1)'
            }
          }
        ]
      }

      this.maleChart.setOption(option)
    },

    initFemaleChart () {
      const chartContainer = document.getElementById(`female-chart-${this.id}`)
      if (!chartContainer) return

      this.femaleChart = echarts.init(chartContainer)

      // 创建刻度线数据 - 总共40个刻度
      const totalTicks = 40
      const activeTicks = Math.round((this.femaleRatio / 100) * totalTicks)
      const tickData = []

      for (let i = 0; i < totalTicks; i++) {
        tickData.push({
          value: 1,
          itemStyle: {
            color: i < activeTicks ? '#FFD700' : 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            borderColor: 'transparent'
          }
        })
      }

      const option = {
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        series: [
          {
            type: 'pie',
            radius: ['75%', '85%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: tickData,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            silent: true,
            itemStyle: {
              borderWidth: 2,
              borderColor: 'rgba(7, 52, 95, 1)'
            }
          }
        ]
      }

      this.femaleChart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.gender-ratio-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  padding: 20px;

  .ratio-item {
    position: relative;
    width: 150px;
    height: 150px;

    .chart-container {
      width: 100%;
      height: 100%;
    }

    .ratio-label {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #fff;
      z-index: 10;

      .percentage {
        display: block;
        font-size: 32px;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 8px;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
      }

      .gender-text {
        display: block;
        font-size: 18px;
        opacity: 0.9;
        font-weight: 500;
      }
    }
  }
}
</style>
