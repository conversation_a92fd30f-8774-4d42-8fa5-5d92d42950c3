<template>
  <div class="horizontal-bar-chart">
    <div class="chart-container">
      <div v-for="(item, index) in chartData" :key="index" class="chart-row">
        <div class="row-label">{{ item.name }}</div>
        <div class="row-bar-container">
          <div class="row-bar">
            <div v-for="(segment, segIndex) in getSegments(item.value)" :key="segIndex" class="bar-segment"
              :class="{ 'filled': segIndex < getFilledSegments(item.value) }"></div>
          </div>
          <div class="row-value">{{ item.value }}人</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HorizontalBarChart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    // 每个分段代表的数值
    segmentValue: {
      type: Number,
      default: 10
    },
    // 最大分段数（固定总分段数，不随数据变化）
    maxSegments: {
      type: Number,
      default: 25
    },
    // 进度条的最大值（用于计算比例，如果不设置则自动计算）
    maxValue: {
      type: Number,
      default: null
    },
    // 最大值的缩放比例（数据最大值的倍数）
    maxValueScale: {
      type: Number,
      default: 1.1
    }
  },
  computed: {
    // 计算总分段数（固定值，不随数据变化）
    totalSegments () {
      return this.maxSegments
    },
    // 动态计算的最大值
    dynamicMaxValue () {
      // 如果手动设置了maxValue，则使用设置的值
      if (this.maxValue !== null) {
        return this.maxValue
      }
      // 否则根据数据自动计算
      if (!this.chartData.length) return 100
      const dataMaxValue = Math.max(...this.chartData.map(item => item.value))
      // 返回数据最大值的倍数，确保最大值不会撑满
      const calculatedMax = Math.ceil(dataMaxValue * this.maxValueScale)
      console.log(`数据最大值: ${dataMaxValue}, 缩放比例: ${this.maxValueScale}, 计算的最大值: ${calculatedMax}`)
      return calculatedMax
    }
  },
  methods: {
    // 生成分段数组
    getSegments (value) {
      const segments = []
      for (let i = 0; i < this.totalSegments; i++) {
        segments.push(i)
      }
      return segments
    },
    // 计算应该填充的分段数
    getFilledSegments (value) {
      if (value <= 0) return 0
      // 计算按比例应该填充的分段数
      const proportionalSegments = Math.floor((value / this.dynamicMaxValue) * this.totalSegments)
      // 调试信息
      console.log(`值: ${value}, 动态最大值: ${this.dynamicMaxValue}, 总分段: ${this.totalSegments}, 计算分段: ${proportionalSegments}`)
      // 确保有数值的项目至少显示1个分段
      return Math.max(1, proportionalSegments)
    }
  }
}
</script>

<style scoped>
.horizontal-bar-chart {
  width: 100%;
  height: 100%;
  padding-top: 10px;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 22px;
}

.chart-row {
  display: flex;
  align-items: center;
  position: relative;
}

.row-label {
  width: 50px;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  flex-shrink: 0;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
}

.row-bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.row-bar {
  flex: 1;
  height: 15px;
  display: flex;
  align-items: center;
  background: #09306B;
  border: 1px solid #979797;
  position: relative;
  overflow: hidden;
  padding: 1px;
  /* 右边斜切效果 */
  /* clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 0 100%); */
}

.bar-segment {
  flex: 1;
  height: 10px;
  background: rgba(0, 100, 200, 0.2);
  transition: all 0.4s ease;
  position: relative;
  border: none;
  /* 倾斜的格子效果 */
  clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%);
  margin-right: 1px;
}

.bar-segment:last-child {
  margin-right: 0;
}

.bar-segment.filled {
  background: linear-gradient(135deg,
      #00D4FF 0%,
      #0099CC 30%,
      #00AADD 70%,
      #33E0FF 100%);
}

.row-value {
  color: #FFFFFF;
  font-size: 14px;
  width: 70px;
  text-align: right;
}

/* 悬停效果 */
.chart-row:hover .row-bar {
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow:
    inset 0 1px 2px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(0, 212, 255, 0.4);
}

.chart-row:hover .bar-segment.filled {
  background: linear-gradient(135deg,
      #33E0FF 0%,
      #00AADD 30%,
      #00BBEE 70%,
      #55F0FF 100%);
  box-shadow:
    0 0 12px rgba(0, 212, 255, 0.7),
    inset 0 1px 2px rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

.chart-row:hover .row-value {
  color: #00D4FF;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
}

.chart-row:hover .row-label {
  color: #00D4FF;
  text-shadow: 0 0 6px rgba(0, 212, 255, 0.4);
}
</style>
