{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue?vue&type=style&index=1&id=7bc2e329&scoped=true&lang=css&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue", "mtime": 1755658080606}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Cjx0ZW1wbGF0ZT48ZGl2IGNsYXNzPSJob3Jpem9udGFsLWJhci1jaGFydCI+PGRpdiBjbGFzcz0iY2hhcnQtY29udGFpbmVyIiA6c3R5bGU9InsgaGVpZ2h0OiBjaGFydEhlaWdodCB9Ij48ZGl2IHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIGNoYXJ0RGF0YSIgOmtleT0iaW5kZXgiIGNsYXNzPSJjaGFydC1yb3ciPjxkaXYgY2xhc3M9InJvdy1sYWJlbCI+IHsKICAgIHsKICAgIGl0ZW0ubmFtZQogIH0KfQoKPC9kaXY+PGRpdiBjbGFzcz0icm93LWJhci1jb250YWluZXIiPjxkaXYgY2xhc3M9InJvdy1iYXIiPjxkaXYgdi1mb3I9IihzZWdtZW50LCBzZWdJbmRleCkgaW4gZ2V0U2VnbWVudHMoaXRlbS52YWx1ZSkiIDprZXk9InNlZ0luZGV4IiBjbGFzcz0iYmFyLXNlZ21lbnQiCgo6Y2xhc3M9InsgJ2ZpbGxlZCc6IHNlZ0luZGV4IDwgTWF0aC5mbG9vcihpdGVtLnZhbHVlIC8gc2VnbWVudFZhbHVlKSB9Ij48L2Rpdj48L2Rpdj48ZGl2IGNsYXNzPSJyb3ctdmFsdWUiPiB7CiAgICB7CiAgICBpdGVtLnZhbHVlCiAgfQp9Cgrkuro8L2Rpdj48L2Rpdj48L2Rpdj48L2Rpdj48L2Rpdj48L3RlbXBsYXRlPjxzY3JpcHQ+ZXhwb3J0IGRlZmF1bHQgewoKICBuYW1lOiAnSG9yaXpvbnRhbEJhckNoYXJ0JywKICBwcm9wczogewogICAgY2hhcnREYXRhOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICAgIGRlZmF1bHQ6ICgpPT4gW10KICAgIH0KCiAgICAsCiAgICBjaGFydEhlaWdodDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICAgZGVmYXVsdDogJzMwMHB4JwogICAgfQoKICAgICwKICAgIC8vIOavj+S4quWIhuauteS7o+ihqOeahOaVsOWAvAogICAgc2VnbWVudFZhbHVlOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgICBkZWZhdWx0OiAxMAogICAgfQoKICAgICwKICAgIC8vIOacgOWkp+WIhuauteaVsAogICAgbWF4U2VnbWVudHM6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICAgIGRlZmF1bHQ6IDIwCiAgICB9CiAgfQoKICAsCiAgY29tcHV0ZWQ6IHsKCiAgICAvLyDorqHnrpfmnIDlpKflgLznlKjkuo7nu5/kuIDliIbmrrXmlbAKICAgIG1heFZhbHVlICgpIHsKICAgICAgaWYgKCAhdGhpcy5jaGFydERhdGEubGVuZ3RoKSByZXR1cm4gMCByZXR1cm4gTWF0aC5tYXgoLi4udGhpcy5jaGFydERhdGEubWFwKGl0ZW09PiBpdGVtLnZhbHVlKSkKICAgIH0KCiAgICAsCiAgICAvLyDorqHnrpfmgLvliIbmrrXmlbAKICAgIHRvdGFsU2VnbWVudHMgKCkgewogICAgICByZXR1cm4gTWF0aC5taW4oTWF0aC5jZWlsKHRoaXMubWF4VmFsdWUgLyB0aGlzLnNlZ21lbnRWYWx1ZSksIHRoaXMubWF4U2VnbWVudHMpCiAgICB9CiAgfQoKICAsCiAgbWV0aG9kczogewoKICAgIC8vIOeUn+aIkOWIhuauteaVsOe7hAogICAgZ2V0U2VnbWVudHMgKHZhbHVlKSB7CiAgICAgIGNvbnN0IHNlZ21lbnRzPVtdIGZvciAobGV0IGk9MDsgaSA8IHRoaXMudG90YWxTZWdtZW50czsgaSsrKSB7CiAgICAgICAgc2VnbWVudHMucHVzaChpKQogICAgICB9CgogICAgICByZXR1cm4gc2VnbWVudHMKICAgIH0KICB9Cn0KCjwvc2NyaXB0PjxzdHlsZSBzY29wZWQ+Lmhvcml6b250YWwtYmFyLWNoYXJ0IHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7CiAgcGFkZGluZzogMTBweCAwOwp9CgouY2hhcnQtY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiAxNXB4OwogIHBhZGRpbmc6IDAgMjBweDsKfQoKLmNoYXJ0LXJvdyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGhlaWdodDogMzVweDsKfQoKLnJvdy1sYWJlbCB7CiAgd2lkdGg6IDgwcHg7CiAgY29sb3I6ICNGRkZGRkY7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiBub3JtYWw7CiAgdGV4dC1hbGlnbjogbGVmdDsKICBmbGV4LXNocmluazogMDsKfQoKLnJvdy1iYXItY29udGFpbmVyIHsKICBmbGV4OiAxOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tbGVmdDogMTVweDsKfQoKLnJvdy1iYXIgewogIGZsZXg6IDE7CiAgaGVpZ2h0OiAyMHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDNweDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsCiAgICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAwJSwKICAgICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KSAxMDAlKTsKICBib3JkZXItcmFkaXVzOiAxMHB4OwogIHBhZGRpbmc6IDJweCA1cHg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5iYXItc2VnbWVudCB7CiAgd2lkdGg6IDEycHg7CiAgaGVpZ2h0OiAxMnB4OwogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsKICBib3JkZXItcmFkaXVzOiAycHg7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0KCi5iYXItc2VnbWVudC5maWxsZWQgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMwMEQ0RkYgMCUsICMwMDk5Q0MgMTAwJSk7CiAgYm94LXNoYWRvdzogMCAwIDhweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjUpOwogIGFuaW1hdGlvbjogZmlsbEFuaW1hdGlvbiAwLjVzIGVhc2Utb3V0Owp9CgouYmFyLXNlZ21lbnQuZmlsbGVkOjpiZWZvcmUgewogIGNvbnRlbnQ6ICcnOwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLAogICAgICB0cmFuc3BhcmVudCAyNSUsCiAgICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSAyNSUsCiAgICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSA1MCUsCiAgICAgIHRyYW5zcGFyZW50IDUwJSwKICAgICAgdHJhbnNwYXJlbnQgNzUlLAogICAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMykgNzUlKTsKICBiYWNrZ3JvdW5kLXNpemU6IDhweCA4cHg7CiAgYm9yZGVyLXJhZGl1czogMnB4Owp9Cgoucm93LXZhbHVlIHsKICB3aWR0aDogNjBweDsKICBjb2xvcjogI0ZGRkZGRjsKICBmb250LXNpemU6IDE0cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgbWFyZ2luLWxlZnQ6IDE1cHg7CiAgZmxleC1zaHJpbms6IDA7Cn0KCkBrZXlmcmFtZXMgZmlsbEFuaW1hdGlvbiB7CiAgMCUgewogICAgdHJhbnNmb3JtOiBzY2FsZVgoMCk7CiAgICBvcGFjaXR5OiAwOwogIH0KCiAgNTAlIHsKICAgIHRyYW5zZm9ybTogc2NhbGVYKDEuMSk7CiAgfQoKICAxMDAlIHsKICAgIHRyYW5zZm9ybTogc2NhbGVYKDEpOwogICAgb3BhY2l0eTogMTsKICB9Cn0KCi8qIOaCrOWBnOaViOaenCAqLwouY2hhcnQtcm93OmhvdmVyIC5iYXItc2VnbWVudC5maWxsZWQgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzM0UwRkYgMCUsICMwMEFBREQgMTAwJSk7CiAgYm94LXNoYWRvdzogMCAwIDEycHggcmdiYSgwLCAyMTIsIDI1NSwgMC44KTsKICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7Cn0KCi5jaGFydC1yb3c6aG92ZXIgLnJvdy12YWx1ZSB7CiAgY29sb3I6ICMwMEQ0RkY7CiAgdGV4dC1zaGFkb3c6IDAgMCA4cHggcmdiYSgwLCAyMTIsIDI1NSwgMC41KTsKfQo="}, {"version": 3, "sources": ["HorizontalBarChart.vue"], "names": [], "mappings": ";AAoQA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "HorizontalBarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"horizontal-bar-chart\">\n    <div class=\"chart-container\" :style=\"{ height: chartHeight }\">\n      <div v-for=\"(item, index) in chartData\" :key=\"index\" class=\"chart-row\">\n        <div class=\"row-label\">{{ item.name }}</div>\n        <div class=\"row-bar-container\">\n          <div class=\"row-bar\">\n            <div v-for=\"(segment, segIndex) in getSegments(item.value)\" :key=\"segIndex\" class=\"bar-segment\"\n              :class=\"{ 'filled': segIndex < Math.floor(item.value / segmentValue) }\"></div>\n          </div>\n          <div class=\"row-value\">{{ item.value }}人</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HorizontalBarChart',\n  props: {\n    chartData: {\n      type: Array,\n      default: () => []\n    },\n    chartHeight: {\n      type: String,\n      default: '300px'\n    },\n    // 每个分段代表的数值\n    segmentValue: {\n      type: Number,\n      default: 10\n    },\n    // 最大分段数\n    maxSegments: {\n      type: Number,\n      default: 20\n    }\n  },\n  data () {\n    return {\n      chart: null\n    }\n  },\n  methods: {\n    // 生成分段数组\n    getSegments (value) {\n      const segments = []\n      for (let i = 0; i < this.totalSegments; i++) {\n        segments.push(i)\n      }\n      return segments\n    }\n  }\n}\n</script>\n\n<style scoped>\n.horizontal-bar-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'HorizontalBarChart',\n  props: {\n    chartId: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      default: () => []\n    },\n    chartHeight: {\n      type: String,\n      default: '300px'\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    // 自定义颜色\n    barColor: {\n      type: String,\n      default: '#00D4FF'\n    },\n    // 是否显示数值\n    showValue: {\n      type: Boolean,\n      default: true\n    },\n    // 是否显示网格线\n    showGrid: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.initChart()\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  watch: {\n    chartData: {\n      handler() {\n        this.updateChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initChart() {\n      const chartDom = document.getElementById(this.chartId)\n      if (!chartDom) return\n      \n      this.chart = echarts.init(chartDom)\n      this.updateChart()\n    },\n    \n    updateChart() {\n      if (!this.chart || !this.chartData.length) return\n      \n      const categories = this.chartData.map(item => item.name)\n      const values = this.chartData.map(item => item.value)\n      const maxValue = Math.max(...values)\n      \n      const option = {\n        backgroundColor: 'transparent',\n        title: {\n          text: this.title,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 16,\n            fontWeight: 'bold'\n          },\n          left: 'center',\n          top: 10\n        },\n        grid: {\n          left: '15%',\n          right: '20%',\n          top: this.title ? '15%' : '5%',\n          bottom: '5%',\n          containLabel: false\n        },\n        xAxis: {\n          type: 'value',\n          show: false,\n          max: maxValue * 1.2\n        },\n        yAxis: {\n          type: 'category',\n          data: categories,\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            color: '#FFFFFF',\n            fontSize: 14,\n            fontWeight: 'normal',\n            margin: 15\n          },\n          inverse: false\n        },\n        series: [\n          {\n            type: 'bar',\n            data: values,\n            barWidth: 20,\n            itemStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 1,\n                y2: 0,\n                colorStops: [\n                  {\n                    offset: 0,\n                    color: this.barColor\n                  },\n                  {\n                    offset: 1,\n                    color: this.barColor + '80' // 添加透明度\n                  }\n                ]\n              },\n              borderRadius: [0, 10, 10, 0]\n            },\n            label: {\n              show: this.showValue,\n              position: 'right',\n              color: '#FFFFFF',\n              fontSize: 14,\n              fontWeight: 'bold',\n              formatter: '{c}人'\n            },\n            backgroundStyle: {\n              color: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: [0, 10, 10, 0]\n            },\n            showBackground: this.showGrid,\n            emphasis: {\n              itemStyle: {\n                color: {\n                  type: 'linear',\n                  x: 0,\n                  y: 0,\n                  x2: 1,\n                  y2: 0,\n                  colorStops: [\n                    {\n                      offset: 0,\n                      color: this.barColor\n                    },\n                    {\n                      offset: 1,\n                      color: '#FFFFFF'\n                    }\n                  ]\n                }\n              }\n            }\n          }\n        ],\n        animationDuration: 1000,\n        animationEasing: 'cubicOut'\n      }\n      \n      this.chart.setOption(option, true)\n    },\n    \n    // 公共方法：重新渲染图表\n    resize() {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n<template><div class=\"horizontal-bar-chart\"><div class=\"chart-container\" :style=\"{ height: chartHeight }\"><div v-for=\"(item, index) in chartData\" :key=\"index\" class=\"chart-row\"><div class=\"row-label\"> {\n    {\n    item.name\n  }\n}\n\n</div><div class=\"row-bar-container\"><div class=\"row-bar\"><div v-for=\"(segment, segIndex) in getSegments(item.value)\" :key=\"segIndex\" class=\"bar-segment\"\n\n:class=\"{ 'filled': segIndex < Math.floor(item.value / segmentValue) }\"></div></div><div class=\"row-value\"> {\n    {\n    item.value\n  }\n}\n\n人</div></div></div></div></div></template><script>export default {\n\n  name: 'HorizontalBarChart',\n  props: {\n    chartData: {\n      type: Array,\n        default: ()=> []\n    }\n\n    ,\n    chartHeight: {\n      type: String,\n        default: '300px'\n    }\n\n    ,\n    // 每个分段代表的数值\n    segmentValue: {\n      type: Number,\n        default: 10\n    }\n\n    ,\n    // 最大分段数\n    maxSegments: {\n      type: Number,\n        default: 20\n    }\n  }\n\n  ,\n  computed: {\n\n    // 计算最大值用于统一分段数\n    maxValue () {\n      if ( !this.chartData.length) return 0 return Math.max(...this.chartData.map(item=> item.value))\n    }\n\n    ,\n    // 计算总分段数\n    totalSegments () {\n      return Math.min(Math.ceil(this.maxValue / this.segmentValue), this.maxSegments)\n    }\n  }\n\n  ,\n  methods: {\n\n    // 生成分段数组\n    getSegments (value) {\n      const segments=[] for (let i=0; i < this.totalSegments; i++) {\n        segments.push(i)\n      }\n\n      return segments\n    }\n  }\n}\n\n</script><style scoped>.horizontal-bar-chart {\n  width: 100%;\n  height: 100%;\n  padding: 10px 0;\n}\n\n.chart-container {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  padding: 0 20px;\n}\n\n.chart-row {\n  display: flex;\n  align-items: center;\n  height: 35px;\n}\n\n.row-label {\n  width: 80px;\n  color: #FFFFFF;\n  font-size: 14px;\n  font-weight: normal;\n  text-align: left;\n  flex-shrink: 0;\n}\n\n.row-bar-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  margin-left: 15px;\n}\n\n.row-bar {\n  flex: 1;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  gap: 3px;\n  background: linear-gradient(90deg,\n      rgba(255, 255, 255, 0.1) 0%,\n      rgba(255, 255, 255, 0.05) 100%);\n  border-radius: 10px;\n  padding: 2px 5px;\n  position: relative;\n  overflow: hidden;\n}\n\n.bar-segment {\n  width: 12px;\n  height: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.bar-segment.filled {\n  background: linear-gradient(135deg, #00D4FF 0%, #0099CC 100%);\n  box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);\n  animation: fillAnimation 0.5s ease-out;\n}\n\n.bar-segment.filled::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg,\n      transparent 25%,\n      rgba(255, 255, 255, 0.3) 25%,\n      rgba(255, 255, 255, 0.3) 50%,\n      transparent 50%,\n      transparent 75%,\n      rgba(255, 255, 255, 0.3) 75%);\n  background-size: 8px 8px;\n  border-radius: 2px;\n}\n\n.row-value {\n  width: 60px;\n  color: #FFFFFF;\n  font-size: 14px;\n  font-weight: bold;\n  text-align: right;\n  margin-left: 15px;\n  flex-shrink: 0;\n}\n\n@keyframes fillAnimation {\n  0% {\n    transform: scaleX(0);\n    opacity: 0;\n  }\n\n  50% {\n    transform: scaleX(1.1);\n  }\n\n  100% {\n    transform: scaleX(1);\n    opacity: 1;\n  }\n}\n\n/* 悬停效果 */\n.chart-row:hover .bar-segment.filled {\n  background: linear-gradient(135deg, #33E0FF 0%, #00AADD 100%);\n  box-shadow: 0 0 12px rgba(0, 212, 255, 0.8);\n  transform: scale(1.1);\n}\n\n.chart-row:hover .row-value {\n  color: #00D4FF;\n  text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);\n}\n</style>\n\n</style>\n"]}]}