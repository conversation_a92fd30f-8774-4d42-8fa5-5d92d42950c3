<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right">
        <div class="header-buttons">
          <el-popover placement="bottom" width="300" trigger="click" popper-class="area-popover"
            v-model="showAreaPopover">
            <div class="area-selector">
              <div class="area-tabs">
                <div class="area-tab" :class="{ 'active': activeTab === 'city' }" @click="activeTab = 'city'">
                  城市
                </div>
                <div class="area-tab" :class="{ 'active': activeTab === 'district' }" @click="activeTab = 'district'">
                  区县
                </div>
              </div>
              <div class="area-content">
                <!-- 城市选择 -->
                <div v-if="activeTab === 'city'" class="area-list">
                  <div class="area-item" v-for="city in cityList" :key="city.code"
                    :class="{ 'selected': city.name === selectedCity }" @click="selectCity(city)">
                    {{ city.name }}
                  </div>
                </div>
                <!-- 区县选择 -->
                <div v-if="activeTab === 'district'" class="area-list">
                  <div class="area-item" v-for="district in districtList" :key="district.code"
                    :class="{ 'selected': district.name === selectedDistrict }" @click="selectDistrict(district)">
                    {{ district.name }}
                  </div>
                </div>
              </div>
            </div>
            <div class="header-btn area-select-btn" slot="reference">
              <span>{{ currentAreaDisplay }}</span>
              <i class="dropdown-icon" :class="{ 'active': showAreaPopover }">▼</i>
            </div>
          </el-popover>
          <div class="header-btn current-module-btn">
            <span>委员统计</span>
          </div>
          <div class="header-btn home-btn" @click="goHome">
            <span>返回首页</span>
          </div>
        </div>
      </div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 委员数量 -->
        <div class="committee-count-section">
          <div class="header_box">
            <span class="header_text_left">委员数量</span>
          </div>
          <div class="count-content">
            <div class="count-item">
              <div class="count-value" style="color: #02FBFB;">{{ memberTotalNum }}</div>
              <img src="../../../assets/largeScreen/icon_member.png" class="count-img">
              <div class="count-label">委员总数</div>
            </div>
            <div class="count-item">
              <div class="count-value" style="color: #F5E74F;">{{ standingMemberTotalNum }}</div>
              <img src="../../../assets/largeScreen/icon_standingMember.png" class="count-img">
              <div class="count-label">政协常委</div>
            </div>
          </div>
        </div>

        <!-- 性别比例 -->
        <div class="gender-ratio-section">
          <div class="header_box">
            <span class="header_text_left">性别比例</span>
          </div>
          <div class="gender-content">
            <GenderRatioChart id="gender-ratio" :male-ratio="100" :female-ratio="60" />
          </div>
        </div>

        <!-- 年龄 -->
        <div class="age-section">
          <div class="header_box">
            <span class="header_text_left">年龄</span>
          </div>
          <div class="age-content">
            <PieChart id="age" :chart-data="ageChartData" :name="ageChartName" />
          </div>
        </div>

        <!-- 学历 -->
        <div class="education-section">
          <div class="header_box">
            <span class="header_text_left">学历</span>
          </div>
          <div class="education-content">
            <HorizontalBarChart id="education-chart" :chart-data="educationData" :max-segments="30"
              bar-color="#00D4FF" />
          </div>
        </div>

        <!-- 党派分布 -->
        <div class="party-distribution-section">
          <div class="header_box">
            <span class="header_text_left">党派分布</span>
          </div>
          <div class="party-content">
            <PieChart3D id="partyDistributionChart" :chart-data="partyData" />
          </div>
        </div>

        <!-- 讨论组人员统计 -->
        <div class="discussion-stats-section">
          <div class="header_box">
            <span class="header_text_left">讨论组人员统计</span>
          </div>
          <div class="discussion-content">
            <BarChart id="discussionGroupChart" :chart-data="discussionGroupData" />
          </div>
        </div>
      </div>

      <div class="right-panel">
        <!-- 界别分析 -->
        <div class="sector-analysis-section">
          <div class="header_box">
            <span class="header_text_left">界别分布</span>
          </div>
          <div class="sector-content">
            <BarScrollChart id="sectorAnalysis" :showCount="30" :chart-data="sectorAnalysisData" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'
import BarScrollChart from '../components/BarScrollChart.vue'
import BarChart from '../components/BarChart.vue'
import PieChart3D from '../components/PieChart3D.vue'
import PieChart from '../components/PieChart.vue'
import HorizontalBarChart from '../components/HorizontalBarChart.vue'
import GenderRatioChart from '../components/GenderRatioChart.vue'

export default {
  name: 'BigScreen',
  components: {
    BarScrollChart,
    BarChart,
    PieChart3D,
    PieChart,
    HorizontalBarChart,
    GenderRatioChart
  },
  data () {
    return {
      currentTime: '',
      // 地区选择相关
      showAreaPopover: false,
      activeTab: 'city',
      selectedCity: '青岛',
      selectedDistrict: '',
      cityList: [
        { name: '青岛', code: 'qingdao' },
        { name: '济南', code: 'jinan' },
        { name: '烟台', code: 'yantai' },
        { name: '潍坊', code: 'weifang' },
        { name: '临沂', code: 'linyi' },
        { name: '淄博', code: 'zibo' }
      ],
      districtList: [
        { name: '市南区', code: 'shinan' },
        { name: '市北区', code: 'shibei' },
        { name: '李沧区', code: 'licang' },
        { name: '崂山区', code: 'laoshan' },
        { name: '城阳区', code: 'chengyang' },
        { name: '即墨区', code: 'jimo' },
        { name: '胶州市', code: 'jiaozhou' },
        { name: '平度市', code: 'pingdu' },
        { name: '莱西市', code: 'laixi' },
        { name: '西海岸新区', code: 'xihaian' }
      ],
      // 学历数据
      educationData: [
        { name: '研究生', value: 84 },
        { name: '本科', value: 165 },
        { name: '大专', value: 500 },
        { name: '高中', value: 200 },
        { name: '职高', value: 160 },
        { name: '初中', value: 90 }
      ],
      // 党派数据
      partyData: [
        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },
        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },
        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },
        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },
        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },
        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },
        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },
        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },
        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },
        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }
      ],
      // 界别分析数据
      sectorAnalysisData: [
        { name: '经济界', value: 32 },
        { name: '教育界', value: 15 },
        { name: '科技界', value: 14 },
        { name: '工商界', value: 13 },
        { name: '医药卫生界', value: 12 },
        { name: '社会科学界', value: 10 },
        { name: '工会', value: 8 },
        { name: '共青团', value: 7 },
        { name: '妇联', value: 6 },
        { name: '科协', value: 5 },
        { name: '台联', value: 7 },
        { name: '侨联', value: 3 },
        { name: '文化艺术界', value: 24 },
        { name: '体育界', value: 16 },
        { name: '少数民族界', value: 20 },
        { name: '宗教界', value: 27 },
        { name: '特邀人士', value: 21 },
        { name: '港澳台侨', value: 5 },
        { name: '对外友好界', value: 19 },
        { name: '社会福利和社会保障界', value: 12 },
        { name: '社会治理和社会组织界', value: 21 },
        { name: '医药卫生界', value: 12 },
        { name: '社会科学界', value: 10 },
        { name: '工会', value: 8 },
        { name: '共青团', value: 7 },
        { name: '妇联', value: 6 },
        { name: '科协', value: 5 },
        { name: '台联', value: 7 },
        { name: '体育界', value: 16 },
        { name: '少数民族界', value: 20 },
        { name: '宗教界', value: 27 },
        { name: '特邀人士', value: 21 },
        { name: '港澳台侨', value: 5 },
        { name: '对外友好界', value: 19 }
      ],
      // 讨论组人员统计数据
      discussionGroupData: [
        { name: '第1组', value: 65 },
        { name: '第2组', value: 42 },
        { name: '第3组', value: 63 },
        { name: '第4组', value: 45 },
        { name: '第5组', value: 68 },
        { name: '第6组', value: 38 },
        { name: '第7组', value: 41 },
        { name: '第8组', value: 39 },
        { name: '第9组', value: 43 },
        { name: '第10组', value: 58 },
        { name: '第11组', value: 36 },
        { name: '第12组', value: 15 },
        { name: '第13组', value: 55 },
        { name: '第14组', value: 42 },
        { name: '第15组', value: 66 },
        { name: '第16组', value: 35 },
        { name: '第17组', value: 28 },
        { name: '第18组', value: 40 },
        { name: '第19组', value: 48 }
      ],
      // 年龄数据
      ageChartData: [
        { name: '29岁以下', value: 20, percentage: '5%' },
        { name: '30-39岁', value: 125, percentage: '30%' },
        { name: '40-49岁', value: 168, percentage: '40%' },
        { name: '50-59岁', value: 85, percentage: '20%' },
        { name: '60岁以上', value: 20, percentage: '5%' }
      ],
      ageChartName: '年龄占比',
      memberTotalNum: 418,
      standingMemberTotalNum: 42
    }
  },
  computed: {
    // 当前显示的地区名称
    currentAreaDisplay () {
      if (this.selectedDistrict) {
        return this.selectedDistrict
      }
      return this.selectedCity
    }
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 返回首页
    goHome () {
      this.$router.push({ path: '/homeBox' })
    },
    // 选择城市
    selectCity (city) {
      this.selectedCity = city.name
      this.selectedDistrict = '' // 清空区县选择
      this.showAreaPopover = false
      // 这里可以添加切换城市后的数据更新逻辑
      console.log('选择了城市:', city)
    },
    // 选择区县
    selectDistrict (district) {
      this.selectedDistrict = district.name
      this.showAreaPopover = false
      // 这里可以添加切换区县后的数据更新逻辑
      console.log('选择了区县:', district)
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .header-buttons {
        display: flex;
        gap: 15px;

        .header-btn {
          height: 42px;
          line-height: 42px;
          padding: 0 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);
            border-color: rgba(0, 181, 254, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);

            &::before {
              left: 100%;
            }
          }

          &.current-module-btn {
            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: bold;
            font-size: 16px;
            color: #FFFFFF;
          }

          &.home-btn {
            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
            font-weight: 400;
            font-size: 16px;
            color: #1FC6FF;
          }

          &.area-select-btn {
            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);
            border: 1px solid rgba(0, 181, 254, 0.5);
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            color: #1FC6FF;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 120px;

            .dropdown-icon {
              margin-left: 8px;
              font-size: 12px;
              transition: transform 0.3s ease;
              color: #1FC6FF;

              &.active {
                transform: rotate(180deg);
              }
            }

            &:hover {
              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);
              border-color: rgba(0, 181, 254, 0.8);
            }
          }
        }
      }
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 20px;
    gap: 20px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 1fr 1fr 1fr;
      gap: 20px;
      height: 100%;
    }

    .right-panel {
      width: 465px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    // 左侧面板样式
    .left-panel {

      // 委员数量
      .committee-count-section {
        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1; // 第一列
        grid-row: 1; // 第一行

        .count-content {
          display: flex;
          justify-content: space-around;
          align-items: center;
          height: 100%;
          margin-top: 30px;

          .count-item {
            text-align: center;

            .count-value {
              font-weight: 500;
              font-size: 32px;
            }

            .count-img {
              width: 118px;
              margin-top: -10px;
              margin-bottom: 10px;
            }

            .count-label {
              font-size: 16px;
              color: #FFFFFF;
            }
          }
        }
      }

      // 性别比例
      .gender-ratio-section {
        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 2; // 第二列
        grid-row: 1; // 第一行

        .gender-content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }

      // 年龄
      .age-section {
        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 3; // 第三列
        grid-row: 1; // 第一行

        .age-content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }

      // 学历
      .education-section {
        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 1; // 第一列
        grid-row: 2; // 第二行

        .education-content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }

      // 党派分布
      .party-distribution-section {
        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）
        grid-row: 2; // 明确指定在第二行

        .party-content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }

      // 讨论人员统计
      .discussion-stats-section {
        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）
        grid-row: 3; // 明确指定在第三行

        .discussion-content {
          margin-top: 50px;
          height: calc(100% - 50px);
        }
      }
    }

    .right-panel {

      // 界别分析
      .sector-analysis-section {
        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        padding: 20px;
        height: 100%;

        .sector-content {
          margin-top: 30px;
          height: calc(100% - 25px);
        }
      }
    }
  }
}

// el-popover 自定义样式
.area-popover {
  background: rgba(7, 52, 95, 0.95) !important;
  border: 1px solid rgba(0, 181, 254, 0.5) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;

  .area-selector {
    .area-tabs {
      display: flex;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      margin-bottom: 12px;

      .area-tab {
        flex: 1;
        padding: 12px 16px;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 2px solid transparent;

        &:hover {
          color: #1FC6FF;
        }

        &.active {
          color: #1FC6FF;
          border-bottom-color: #1FC6FF;
          font-weight: bold;
        }
      }
    }

    .area-content {
      max-height: 200px;
      overflow-y: auto;

      .area-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;

        .area-item {
          padding: 10px 12px;
          color: #FFFFFF;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 4px;
          text-align: center;
          border: 1px solid transparent;

          &:hover {
            background: rgba(0, 181, 254, 0.2);
            color: #1FC6FF;
            border-color: rgba(0, 181, 254, 0.3);
          }

          &.selected {
            background: rgba(0, 181, 254, 0.4);
            color: #1FC6FF;
            font-weight: bold;
            border-color: #1FC6FF;
          }
        }
      }

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 181, 254, 0.5);
        border-radius: 2px;

        &:hover {
          background: rgba(0, 181, 254, 0.7);
        }
      }
    }
  }
}
</style>
