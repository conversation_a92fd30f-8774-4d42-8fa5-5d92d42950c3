{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1755661368538}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,gBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAJ;MACAG,YADA;MAEAC;IAFA,CALA;IASAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA;EATA,CAFA;;EAiBAC;IACA;MACAC,WADA;MAEAC,wBAFA;MAEA;MACAC,yBAHA;MAGA;MACA;MACAC,SACA,SADA,EACA,SADA,EACA,SADA,EACA,SADA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAKA,SALA,EAKA,SALA,EAKA,SALA,EAKA,SALA;IALA;EAaA,CA/BA;;EAgCAC;IACA;EACA,CAlCA;;EAmCAC;IACA;IACA;MACAC;IACA;;IACA;MACA;IACA;EACA,CA3CA;;EA4CAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;MACA;MACA;MACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,qCAHA;UAIAC,sBAJA;UAKAC,cALA;UAMAC;YACAC;UADA;QANA,CADA;QAWAC;UACAC,kBADA;UAEAC,sDAFA;UAGAC,aAHA;UAIAC,YAJA;UAKAC,aALA;UAMAC,cANA;UAOAC,oDAPA;UAQAT;YACAC,aADA;YAEAS,YAFA;YAGAC;UAHA,CARA;UAaAf;YACA;YACA;UACA;QAhBA,CAXA;QA6BAgB,SACA;UACApC,eADA;UAEAG,WAFA;UAGAkC,2EAHA;UAIAC,2EAJA;UAKAC,wBALA;UAMAC;YACAC,WADA;YAEAC;UAFA,CANA;UAUAC;YACAC,UADA;YAEAC,kBAFA;YAGAX,YAHA;YAIAT,aAJA;YAKAL;UALA,CAVA;UAiBA0B;YACAF;UADA,CAjBA;UAoBAG;YACAxB,cADA;YAEAD,sBAFA,CAEA;;UAFA,CApBA;UAwBAf;YACAyC,iBADA;YAEAhD,eAFA;YAGA+C;cAAAtB;YAAA;UAHA;QAxBA,CADA,EA+BA;UACAtB,WADA;UAEAkC,2EAFA;UAGAC,2EAHA;UAIA/B,OACA;YACAyC,UADA;YAEAD;cACAtB;YADA;UAFA,CADA,CAJA;UAYAkB;YACAC;UADA;QAZA,CA/BA,CA7BA;QA6EAK,UACA;UACA9C,cADA;UAEA+C,uDAFA;UAGArB,sDAHA;UAIAsB;YACAC,KADA;YAEAC,KAFA;YAGAC;UAHA,CAJA;UASAC;YACAC,YADA;YAEAC,YAFA;YAGAC;cACAvD,cADA;cAEAwD,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAAvC;cAAA,CADA,EAEA;gBAAAuC;gBAAAvC;cAAA,CAFA;YANA;UAHA,CATA;UAwBAwC,KAxBA;UAyBAC,YAzBA;UA0BArB;QA1BA,CADA;MA7EA;MA4GA,6BAhHA,CAiHA;;MACAsB;QACA;UACA;QACA;MACA,CAJA,EAlHA,CAwHA;;MACA,0BAzHA,CA2HA;;MACA;QACA;MACA,CAFA;MAIA;QACA;MACA,CAFA;IAGA,CAvIA;;IAyIA;IACAC;MACA;MAEA;QACA;QACA;UACA;YACAjE,gBADA;YAEAkE,cAFA;YAGAC;UAHA;QAKA,CARA,CAUA;;;QACA;QACA;UACAnE,iBADA;UAEAkE,cAFA;UAGAC;QAHA;MAKA,CAjBA,EAiBA,IAjBA,EAHA,CAoBA;IACA,CA/JA;;IAiKA;IACAC;MACA;QACAzD;QACA;MACA,CAJA,CAKA;;;MACA;QACA;UACAX,gBADA;UAEAkE,cAFA;UAGAC;QAHA;QAKA;MACA;IACA;;EAhLA;AA5CA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "data", "chart", "autoHighlightTimer", "currentHighlightIndex", "colors", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "getColor", "initChart", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "textStyle", "color", "legend", "orient", "right", "top", "itemWidth", "itemHeight", "icon", "itemGap", "fontSize", "fontFamily", "series", "radius", "center", "avoidLabelOverlap", "emphasis", "scale", "scaleSize", "label", "show", "position", "labelLine", "itemStyle", "value", "graphic", "left", "shape", "cx", "cy", "r", "style", "fill", "lineWidth", "stroke", "x", "y", "x2", "y2", "colorStops", "offset", "z", "silent", "window", "startAutoHighlight", "seriesIndex", "dataIndex", "stopAutoHighlight"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["PieChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'PieC<PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#26C6DA', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index) {\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'vertical',\n          right: this.id === 'proposal-statistics' ? '0%' : '5%',\n          top: 'center',\n          itemWidth: 5,\n          itemHeight: 5,\n          icon: 'circle',\n          itemGap: this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            return `${name}  ${item ? item.value : ''}%`\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['60%', '85%'] : ['55%', '80%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用你的大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['94%', '95%'] : ['88%', '89%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'proposal-statistics' ? '12%' : '17%',\n            top: this.id === 'proposal-statistics' ? '23%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'proposal-statistics' ? 40 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}