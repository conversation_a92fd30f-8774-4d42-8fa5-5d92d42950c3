{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1755654714324}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,gBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAJ;MACAG,YADA;MAEAC;IAFA,CALA;IASAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA;EATA,CAFA;;EAiBAC;IACA;MACAC,WADA;MAEA;MACAC,SACA,SADA,EACA,SADA,EACA,SADA,EACA,SADA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAKA,SALA,EAKA,SALA,EAKA,SALA,EAKA,SALA;IAHA;EAWA,CA7BA;;EA8BAC;IACA;EACA,CAhCA;;EAiCAC;IACA;MACA;IACA;EACA,CArCA;;EAsCAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;MACA;MACA;MACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,qCAHA;UAIAC,sBAJA;UAKAC,cALA;UAMAC;YACAC;UADA;QANA,CADA;QAWAC;UACAC,kBADA;UAEAC,sDAFA;UAGAC,aAHA;UAIAC,YAJA;UAKAC,aALA;UAMAC,cANA;UAOAC,oDAPA;UAQAT;YACAC,aADA;YAEAS,YAFA;YAGAC;UAHA,CARA;UAaAf;YACA;YACA;UACA;QAhBA,CAXA;QA6BAgB,SACA;UACAjC,eADA;UAEAG,WAFA;UAGA+B,2EAHA;UAIAC,2EAJA;UAKAC,wBALA;UAMAC;YACAC,WADA;YAEAC;UAFA,CANA;UAUAC;YACAC,UADA;YAEAC,kBAFA;YAGAX,YAHA;YAIAT,aAJA;YAKAL;UALA,CAVA;UAiBA0B;YACAF;UADA,CAjBA;UAoBAG;YACAxB,cADA;YAEAD,sBAFA,CAEA;;UAFA,CApBA;UAwBAZ;YACAsC,iBADA;YAEA7C,eAFA;YAGA4C;cAAAtB;YAAA;UAHA;QAxBA,CADA,EA+BA;UACAnB,WADA;UAEA+B,2EAFA;UAGAC,2EAHA;UAIA5B,OACA;YACAsC,UADA;YAEAD;cACAtB;YADA;UAFA,CADA,CAJA;UAYAkB;YACAC;UADA;QAZA,CA/BA,CA7BA;QA6EAK,UACA;UACA3C,cADA;UAEA4C,uDAFA;UAGArB,sDAHA;UAIAsB;YACAC,KADA;YAEAC,KAFA;YAGAC;UAHA,CAJA;UASAC;YACAC,YADA;YAEAC,YAFA;YAGAC;cACApD,cADA;cAEAqD,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAAvC;cAAA,CADA,EAEA;gBAAAuC;gBAAAvC;cAAA,CAFA;YANA;UAHA,CATA;UAwBAwC,KAxBA;UAyBAC,YAzBA;UA0BArB;QA1BA,CADA;MA7EA;MA4GA,6BAhHA,CAiHA;;MACAsB;QACA;UACA;QACA;MACA,CAJA;IAKA;;EA3HA;AAtCA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "data", "chart", "colors", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getColor", "initChart", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "textStyle", "color", "legend", "orient", "right", "top", "itemWidth", "itemHeight", "icon", "itemGap", "fontSize", "fontFamily", "series", "radius", "center", "avoidLabelOverlap", "emphasis", "scale", "scaleSize", "label", "show", "position", "labelLine", "itemStyle", "value", "graphic", "left", "shape", "cx", "cy", "r", "style", "fill", "lineWidth", "stroke", "x", "y", "x2", "y2", "colorStops", "offset", "z", "silent", "window"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["PieChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: '<PERSON><PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#26C6DA', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index) {\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'vertical',\n          right: this.id === 'proposal-statistics' ? '0%' : '5%',\n          top: 'center',\n          itemWidth: 5,\n          itemHeight: 5,\n          icon: 'circle',\n          itemGap: this.id === 'proposal-statistics' ? 12 : 25,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            return `${name}  ${item ? item.value : ''}%`\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['60%', '85%'] : ['55%', '80%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: this.name\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: 3,\n              borderColor: '#07345F' // 用你的大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index) }\n            }))\n          },\n          {\n            type: 'pie',\n            radius: this.id === 'proposal-statistics' ? ['94%', '95%'] : ['88%', '89%'],\n            center: this.id === 'proposal-statistics' ? ['22%', '50%'] : ['30%', '50%'],\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }\n        ],\n        graphic: [\n          {\n            type: 'circle',\n            left: this.id === 'proposal-statistics' ? '12%' : '17%',\n            top: this.id === 'proposal-statistics' ? '23%' : '26%',\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.id === 'proposal-statistics' ? 40 : 50\n            },\n            style: {\n              fill: 'none',\n              lineWidth: 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}