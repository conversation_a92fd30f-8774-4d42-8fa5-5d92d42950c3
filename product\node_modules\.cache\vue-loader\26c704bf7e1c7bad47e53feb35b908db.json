{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=style&index=0&id=2c47cac9&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755676681593}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["committeeStatisticsBox.vue"], "names": [], "mappings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file": "committeeStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"xyl-region\">\r\n            <el-popover placement=\"bottom-start\" trigger=\"click\" transition=\"zy-el-zoom-in-top\"\r\n              popper-class=\"xyl-region-popover\" v-model=\"showAreaPopover\">\r\n              <el-scrollbar class=\"xyl-region-tree\">\r\n                <el-tree ref=\"treeRef\" highlight-current :data=\"treeData\" :props=\"treeProps\" node-key=\"code\"\r\n                  @node-click=\"handleNodeClick\" :default-expanded-keys=\"['qingdao']\" />\r\n              </el-scrollbar>\r\n              <!-- <el-scrollbar class=\"region-tree\">\r\n              <el-tree :data=\"treeData\" :props=\"treeProps\" highlight-current node-key=\"code\"\r\n                :default-expanded-keys=\"['qingdao']\" :current-node-key=\"selectedDistrictCode\"\r\n                @node-click=\"handleNodeClick\" class=\"area-tree\">\r\n              </el-tree>\r\n            </el-scrollbar> -->\r\n              <div class=\"header-btn area-select-btn\" slot=\"reference\">\r\n                <span>{{ selectedArea }}</span>\r\n                <i class=\"dropdown-icon\" :class=\"{ 'active': showAreaPopover }\">▼</i>\r\n              </div>\r\n            </el-popover>\r\n          </div>\r\n\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #02FBFB;\">{{ memberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_member.png\" class=\"count-img\">\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #F5E74F;\">{{ standingMemberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_standingMember.png\" class=\"count-img\">\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" :male-ratio=\"100\" :female-ratio=\"60\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" :chart-data=\"ageChartData\" :name=\"ageChartName\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      selectedArea: '青岛',\r\n      selectedDistrictCode: '',\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'name'\r\n      },\r\n      treeData: [\r\n        {\r\n          name: '青岛',\r\n          code: 'qingdao',\r\n          children: [\r\n            { name: '市南区', code: 'shinan' },\r\n            { name: '市北区', code: 'shibei' },\r\n            { name: '李沧区', code: 'licang' },\r\n            { name: '崂山区', code: 'laoshan' },\r\n            { name: '城阳区', code: 'chengyang' },\r\n            { name: '即墨区', code: 'jimo' },\r\n            { name: '胶州市', code: 'jiaozhou' },\r\n            { name: '平度市', code: 'pingdu' },\r\n            { name: '莱西市', code: 'laixi' },\r\n            { name: '西海岸新区', code: 'xihaian' }\r\n          ]\r\n        }\r\n      ],\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '研究生', value: 84 },\r\n        { name: '本科', value: 165 },\r\n        { name: '大专', value: 500 },\r\n        { name: '高中', value: 200 },\r\n        { name: '职高', value: 160 },\r\n        { name: '初中', value: 90 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [\r\n        { name: '第1组', value: 65 },\r\n        { name: '第2组', value: 42 },\r\n        { name: '第3组', value: 63 },\r\n        { name: '第4组', value: 45 },\r\n        { name: '第5组', value: 68 },\r\n        { name: '第6组', value: 38 },\r\n        { name: '第7组', value: 41 },\r\n        { name: '第8组', value: 39 },\r\n        { name: '第9组', value: 43 },\r\n        { name: '第10组', value: 58 },\r\n        { name: '第11组', value: 36 },\r\n        { name: '第12组', value: 15 },\r\n        { name: '第13组', value: 55 },\r\n        { name: '第14组', value: 42 },\r\n        { name: '第15组', value: 66 },\r\n        { name: '第16组', value: 35 },\r\n        { name: '第17组', value: 28 },\r\n        { name: '第18组', value: 40 },\r\n        { name: '第19组', value: 48 }\r\n      ],\r\n      // 年龄数据\r\n      ageChartData: [\r\n        { name: '29岁以下', value: 20, percentage: '5%' },\r\n        { name: '30-39岁', value: 125, percentage: '30%' },\r\n        { name: '40-49岁', value: 168, percentage: '40%' },\r\n        { name: '50-59岁', value: 85, percentage: '20%' },\r\n        { name: '60岁以上', value: 20, percentage: '5%' }\r\n      ],\r\n      ageChartName: '年龄占比',\r\n      memberTotalNum: 418,\r\n      standingMemberTotalNum: 42\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 允许选择所有节点（包括父级青岛）\r\n      this.selectedArea = data.name\r\n      this.selectedDistrictCode = data.code\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换地区后的数据更新逻辑\r\n      console.log('选择了地区:', data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n\r\n        .xyl-region {\r\n          display: inline-block;\r\n          margin: auto;\r\n          padding: 0 6px;\r\n\r\n          .xyl-region-view {\r\n            display: flex;\r\n            align-items: center;\r\n            height: var(--zy-height);\r\n\r\n            .xyl-region-img {\r\n              width: 24px;\r\n              height: 24px;\r\n            }\r\n\r\n            .xyl-region-name {\r\n              color: #fff;\r\n              padding: 0 6px;\r\n              line-height: var(--zy-line-height);\r\n              font-size: var(--zy-name-font-size);\r\n            }\r\n\r\n            .xyl-region-icon {\r\n              width: 24px;\r\n              color: #fff;\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .el-icon {\r\n                transition: all 0.6s;\r\n                transform: rotateZ(0deg);\r\n              }\r\n            }\r\n\r\n            .xyl-region-is-icon {\r\n              .el-icon {\r\n                transition: all 0.6s;\r\n                transform: rotateZ(-180deg);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .xyl-region-popover {\r\n          width: 290px !important;\r\n          padding: 0 !important;\r\n\r\n          .el-scrollbar__wrap {\r\n            max-height: 220px;\r\n          }\r\n\r\n          .el-scrollbar__view {\r\n            padding: var(--zy-distance-five) 0;\r\n          }\r\n\r\n          .xyl-region-tree {\r\n            width: 100%;\r\n\r\n            .el-tree-node.is-current {\r\n              &>.el-tree-node__content {\r\n                .el-tree-node__label {\r\n                  color: var(--zy-el-color-primary);\r\n                }\r\n              }\r\n            }\r\n\r\n            .el-tree-node__content {\r\n              height: var(--zy-height);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-weight: 500;\r\n              font-size: 32px;\r\n            }\r\n\r\n            .count-img {\r\n              width: 118px;\r\n              margin-top: -10px;\r\n              margin-bottom: 10px;\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// el-popover 自定义样式 - 使用更高优先级\r\n.el-popover.area-popover {\r\n  width: 290px !important;\r\n  padding: 0 !important;\r\n  background: rgba(7, 52, 95, 0.95) !important;\r\n  border: 1px solid rgba(0, 181, 254, 0.5) !important;\r\n  border-radius: 8px !important;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\r\n\r\n  .area-tree-container {\r\n    padding: 8px 0;\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n\r\n    .area-tree {\r\n      background: transparent;\r\n      color: #FFFFFF;\r\n\r\n      // 树节点样式\r\n      .el-tree-node {\r\n        margin-bottom: 4px;\r\n\r\n        .el-tree-node__content {\r\n          background: transparent;\r\n          color: #FFFFFF;\r\n          height: 40px;\r\n          line-height: 40px;\r\n          border-radius: 6px;\r\n          padding: 0 12px;\r\n          transition: all 0.3s ease;\r\n          border: 1px solid transparent;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.15) 0%, rgba(0, 120, 220, 0.15) 100%);\r\n            border-color: rgba(0, 181, 254, 0.3);\r\n            color: #1FC6FF;\r\n            transform: translateX(2px);\r\n          }\r\n\r\n          .el-tree-node__expand-icon {\r\n            color: #1FC6FF;\r\n            font-size: 12px;\r\n            margin-right: 8px;\r\n            transition: all 0.3s ease;\r\n          }\r\n\r\n          .el-tree-node__label {\r\n            color: inherit;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            transition: all 0.3s ease;\r\n          }\r\n        }\r\n\r\n        // 当前选中节点\r\n        &.is-current>.el-tree-node__content {\r\n          background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n          border-color: #1FC6FF;\r\n          color: #1FC6FF;\r\n          font-weight: bold;\r\n          box-shadow: 0 2px 8px rgba(0, 181, 254, 0.2);\r\n\r\n          .el-tree-node__label {\r\n            font-weight: bold;\r\n          }\r\n        }\r\n\r\n        // 父节点样式（青岛）\r\n        &:first-child {\r\n          .el-tree-node__content {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.1) 0%, rgba(0, 120, 220, 0.1) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.2);\r\n            font-weight: 500;\r\n\r\n            .el-tree-node__label {\r\n              font-size: 15px;\r\n              font-weight: 500;\r\n              color: #1FC6FF;\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n              border-color: rgba(0, 181, 254, 0.5);\r\n            }\r\n          }\r\n\r\n          &.is-current>.el-tree-node__content {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.4) 0%, rgba(0, 120, 220, 0.4) 100%);\r\n            border-color: #1FC6FF;\r\n            box-shadow: 0 3px 12px rgba(0, 181, 254, 0.3);\r\n          }\r\n        }\r\n\r\n        // 子节点样式（区县）\r\n        .el-tree-node__children {\r\n          margin-top: 4px;\r\n          padding-left: 20px;\r\n          border-left: 2px solid rgba(0, 181, 254, 0.2);\r\n          margin-left: 12px;\r\n\r\n          .el-tree-node {\r\n            .el-tree-node__content {\r\n              padding-left: 16px;\r\n              height: 36px;\r\n              line-height: 36px;\r\n\r\n              .el-tree-node__label {\r\n                font-size: 13px;\r\n                color: rgba(255, 255, 255, 0.9);\r\n              }\r\n\r\n              &:hover {\r\n                .el-tree-node__label {\r\n                  color: #1FC6FF;\r\n                }\r\n              }\r\n            }\r\n\r\n            &.is-current>.el-tree-node__content {\r\n              .el-tree-node__label {\r\n                color: #1FC6FF;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 隐藏叶子节点的展开图标\r\n      .el-tree-node__expand-icon.is-leaf {\r\n        color: transparent;\r\n        cursor: default;\r\n      }\r\n    }\r\n\r\n    // 自定义滚动条\r\n    &::-webkit-scrollbar {\r\n      width: 6px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-track {\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 3px;\r\n    }\r\n\r\n    &::-webkit-scrollbar-thumb {\r\n      background: linear-gradient(180deg, rgba(0, 181, 254, 0.6) 0%, rgba(0, 120, 220, 0.6) 100%);\r\n      border-radius: 3px;\r\n\r\n      &:hover {\r\n        background: linear-gradient(180deg, rgba(0, 181, 254, 0.8) 0%, rgba(0, 120, 220, 0.8) 100%);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}