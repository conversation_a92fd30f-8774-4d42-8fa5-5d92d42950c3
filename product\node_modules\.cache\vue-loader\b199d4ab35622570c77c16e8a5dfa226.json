{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755674589822}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["committeeStatisticsBox.vue"], "names": [], "mappings": ";AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "committeeStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <el-popover placement=\"bottom\" width=\"300\" trigger=\"click\" popper-class=\"area-popover\"\r\n            v-model=\"showAreaPopover\">\r\n            <div class=\"area-selector\">\r\n              <div class=\"area-tabs\">\r\n                <div class=\"area-tab\" :class=\"{ 'active': activeTab === 'city' }\" @click=\"activeTab = 'city'\">\r\n                  城市\r\n                </div>\r\n                <div class=\"area-tab\" :class=\"{ 'active': activeTab === 'district' }\" @click=\"activeTab = 'district'\">\r\n                  区县\r\n                </div>\r\n              </div>\r\n              <div class=\"area-content\">\r\n                <!-- 城市选择 -->\r\n                <div v-if=\"activeTab === 'city'\" class=\"area-list\">\r\n                  <div class=\"area-item\" v-for=\"city in cityList\" :key=\"city.code\"\r\n                    :class=\"{ 'selected': city.name === selectedCity }\" @click=\"selectCity(city)\">\r\n                    {{ city.name }}\r\n                  </div>\r\n                </div>\r\n                <!-- 区县选择 -->\r\n                <div v-if=\"activeTab === 'district'\" class=\"area-list\">\r\n                  <div class=\"area-item\" v-for=\"district in districtList\" :key=\"district.code\"\r\n                    :class=\"{ 'selected': district.name === selectedDistrict }\" @click=\"selectDistrict(district)\">\r\n                    {{ district.name }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"header-btn area-select-btn\" slot=\"reference\">\r\n              <span>{{ currentAreaDisplay }}</span>\r\n              <i class=\"dropdown-icon\" :class=\"{ 'active': showAreaPopover }\">▼</i>\r\n            </div>\r\n          </el-popover>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #02FBFB;\">{{ memberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_member.png\" class=\"count-img\">\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #F5E74F;\">{{ standingMemberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_standingMember.png\" class=\"count-img\">\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" :male-ratio=\"100\" :female-ratio=\"60\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" :chart-data=\"ageChartData\" :name=\"ageChartName\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      activeTab: 'city',\r\n      selectedCity: '青岛',\r\n      selectedDistrict: '',\r\n      cityList: [\r\n        { name: '青岛', code: 'qingdao' },\r\n        { name: '济南', code: 'jinan' },\r\n        { name: '烟台', code: 'yantai' },\r\n        { name: '潍坊', code: 'weifang' },\r\n        { name: '临沂', code: 'linyi' },\r\n        { name: '淄博', code: 'zibo' }\r\n      ],\r\n      districtList: [\r\n        { name: '市南区', code: 'shinan' },\r\n        { name: '市北区', code: 'shibei' },\r\n        { name: '李沧区', code: 'licang' },\r\n        { name: '崂山区', code: 'laoshan' },\r\n        { name: '城阳区', code: 'chengyang' },\r\n        { name: '即墨区', code: 'jimo' },\r\n        { name: '胶州市', code: 'jiaozhou' },\r\n        { name: '平度市', code: 'pingdu' },\r\n        { name: '莱西市', code: 'laixi' },\r\n        { name: '西海岸新区', code: 'xihaian' }\r\n      ],\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '研究生', value: 84 },\r\n        { name: '本科', value: 165 },\r\n        { name: '大专', value: 500 },\r\n        { name: '高中', value: 200 },\r\n        { name: '职高', value: 160 },\r\n        { name: '初中', value: 90 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [\r\n        { name: '第1组', value: 65 },\r\n        { name: '第2组', value: 42 },\r\n        { name: '第3组', value: 63 },\r\n        { name: '第4组', value: 45 },\r\n        { name: '第5组', value: 68 },\r\n        { name: '第6组', value: 38 },\r\n        { name: '第7组', value: 41 },\r\n        { name: '第8组', value: 39 },\r\n        { name: '第9组', value: 43 },\r\n        { name: '第10组', value: 58 },\r\n        { name: '第11组', value: 36 },\r\n        { name: '第12组', value: 15 },\r\n        { name: '第13组', value: 55 },\r\n        { name: '第14组', value: 42 },\r\n        { name: '第15组', value: 66 },\r\n        { name: '第16组', value: 35 },\r\n        { name: '第17组', value: 28 },\r\n        { name: '第18组', value: 40 },\r\n        { name: '第19组', value: 48 }\r\n      ],\r\n      // 年龄数据\r\n      ageChartData: [\r\n        { name: '29岁以下', value: 20, percentage: '5%' },\r\n        { name: '30-39岁', value: 125, percentage: '30%' },\r\n        { name: '40-49岁', value: 168, percentage: '40%' },\r\n        { name: '50-59岁', value: 85, percentage: '20%' },\r\n        { name: '60岁以上', value: 20, percentage: '5%' }\r\n      ],\r\n      ageChartName: '年龄占比',\r\n      memberTotalNum: 418,\r\n      standingMemberTotalNum: 42\r\n    }\r\n  },\r\n  computed: {\r\n    // 当前显示的地区名称\r\n    currentAreaDisplay () {\r\n      if (this.selectedDistrict) {\r\n        return this.selectedDistrict\r\n      }\r\n      return this.selectedCity\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 选择城市\r\n    selectCity (city) {\r\n      this.selectedCity = city.name\r\n      this.selectedDistrict = '' // 清空区县选择\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换城市后的数据更新逻辑\r\n      console.log('选择了城市:', city)\r\n    },\r\n    // 选择区县\r\n    selectDistrict (district) {\r\n      this.selectedDistrict = district.name\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换区县后的数据更新逻辑\r\n      console.log('选择了区县:', district)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-weight: 500;\r\n              font-size: 32px;\r\n            }\r\n\r\n            .count-img {\r\n              width: 118px;\r\n              margin-top: -10px;\r\n              margin-bottom: 10px;\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// el-popover 自定义样式\r\n.area-popover {\r\n  background: rgba(7, 52, 95, 0.95) !important;\r\n  border: 1px solid rgba(0, 181, 254, 0.5) !important;\r\n  border-radius: 8px !important;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\r\n\r\n  .area-selector {\r\n    .area-tabs {\r\n      display: flex;\r\n      border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n      margin-bottom: 12px;\r\n\r\n      .area-tab {\r\n        flex: 1;\r\n        padding: 12px 16px;\r\n        text-align: center;\r\n        color: rgba(255, 255, 255, 0.7);\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        border-bottom: 2px solid transparent;\r\n\r\n        &:hover {\r\n          color: #1FC6FF;\r\n        }\r\n\r\n        &.active {\r\n          color: #1FC6FF;\r\n          border-bottom-color: #1FC6FF;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n\r\n    .area-content {\r\n      max-height: 200px;\r\n      overflow-y: auto;\r\n\r\n      .area-list {\r\n        display: grid;\r\n        grid-template-columns: repeat(2, 1fr);\r\n        gap: 8px;\r\n\r\n        .area-item {\r\n          padding: 10px 12px;\r\n          color: #FFFFFF;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          border-radius: 4px;\r\n          text-align: center;\r\n          border: 1px solid transparent;\r\n\r\n          &:hover {\r\n            background: rgba(0, 181, 254, 0.2);\r\n            color: #1FC6FF;\r\n            border-color: rgba(0, 181, 254, 0.3);\r\n          }\r\n\r\n          &.selected {\r\n            background: rgba(0, 181, 254, 0.4);\r\n            color: #1FC6FF;\r\n            font-weight: bold;\r\n            border-color: #1FC6FF;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 自定义滚动条\r\n      &::-webkit-scrollbar {\r\n        width: 4px;\r\n      }\r\n\r\n      &::-webkit-scrollbar-track {\r\n        background: rgba(255, 255, 255, 0.1);\r\n        border-radius: 2px;\r\n      }\r\n\r\n      &::-webkit-scrollbar-thumb {\r\n        background: rgba(0, 181, 254, 0.5);\r\n        border-radius: 2px;\r\n\r\n        &:hover {\r\n          background: rgba(0, 181, 254, 0.7);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}