{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755674806597}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["committeeStatisticsBox.vue"], "names": [], "mappings": ";AA0HA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "committeeStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <el-popover placement=\"bottom\" width=\"280\" trigger=\"click\" popper-class=\"area-popover\"\r\n            v-model=\"showAreaPopover\">\r\n            <div class=\"area-tree-container\">\r\n              <el-tree :data=\"treeData\" :props=\"treeProps\" node-key=\"code\" :default-expanded-keys=\"['qingdao']\"\r\n                :current-node-key=\"selectedDistrictCode\" @node-click=\"handleNodeClick\" class=\"area-tree\">\r\n              </el-tree>\r\n            </div>\r\n            <div class=\"header-btn area-select-btn\" slot=\"reference\">\r\n              <span>{{ selectedArea }}</span>\r\n              <i class=\"dropdown-icon\" :class=\"{ 'active': showAreaPopover }\">▼</i>\r\n            </div>\r\n          </el-popover>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #02FBFB;\">{{ memberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_member.png\" class=\"count-img\">\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #F5E74F;\">{{ standingMemberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_standingMember.png\" class=\"count-img\">\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" :male-ratio=\"100\" :female-ratio=\"60\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" :chart-data=\"ageChartData\" :name=\"ageChartName\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      selectedArea: '青岛',\r\n      selectedDistrictCode: '',\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'name'\r\n      },\r\n      treeData: [\r\n        {\r\n          name: '青岛',\r\n          code: 'qingdao',\r\n          children: [\r\n            { name: '市南区', code: 'shinan' },\r\n            { name: '市北区', code: 'shibei' },\r\n            { name: '李沧区', code: 'licang' },\r\n            { name: '崂山区', code: 'laoshan' },\r\n            { name: '城阳区', code: 'chengyang' },\r\n            { name: '即墨区', code: 'jimo' },\r\n            { name: '胶州市', code: 'jiaozhou' },\r\n            { name: '平度市', code: 'pingdu' },\r\n            { name: '莱西市', code: 'laixi' },\r\n            { name: '西海岸新区', code: 'xihaian' }\r\n          ]\r\n        }\r\n      ],\r\n      // 学历数据\r\n      educationData: [\r\n        { name: '研究生', value: 84 },\r\n        { name: '本科', value: 165 },\r\n        { name: '大专', value: 500 },\r\n        { name: '高中', value: 200 },\r\n        { name: '职高', value: 160 },\r\n        { name: '初中', value: 90 }\r\n      ],\r\n      // 党派数据\r\n      partyData: [\r\n        { name: '中国共产党', value: 32, percentage: 15, color: '#FF6B6B' },\r\n        { name: '民革', value: 15, percentage: 8, color: '#4ECDC4' },\r\n        { name: '民盟', value: 14, percentage: 7, color: '#45B7D1' },\r\n        { name: '民建', value: 13, percentage: 6, color: '#96CEB4' },\r\n        { name: '民进', value: 12, percentage: 5, color: '#FFEAA7' },\r\n        { name: '农工党', value: 10, percentage: 4, color: '#DDA0DD' },\r\n        { name: '致公党', value: 8, percentage: 3, color: '#98D8C8' },\r\n        { name: '九三学社', value: 7, percentage: 3, color: '#F7DC6F' },\r\n        { name: '台盟', value: 6, percentage: 2, color: '#BB8FCE' },\r\n        { name: '无党派人士', value: 5, percentage: 2, color: '#85C1E9' }\r\n      ],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        { name: '经济界', value: 32 },\r\n        { name: '教育界', value: 15 },\r\n        { name: '科技界', value: 14 },\r\n        { name: '工商界', value: 13 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '侨联', value: 3 },\r\n        { name: '文化艺术界', value: 24 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 },\r\n        { name: '社会福利和社会保障界', value: 12 },\r\n        { name: '社会治理和社会组织界', value: 21 },\r\n        { name: '医药卫生界', value: 12 },\r\n        { name: '社会科学界', value: 10 },\r\n        { name: '工会', value: 8 },\r\n        { name: '共青团', value: 7 },\r\n        { name: '妇联', value: 6 },\r\n        { name: '科协', value: 5 },\r\n        { name: '台联', value: 7 },\r\n        { name: '体育界', value: 16 },\r\n        { name: '少数民族界', value: 20 },\r\n        { name: '宗教界', value: 27 },\r\n        { name: '特邀人士', value: 21 },\r\n        { name: '港澳台侨', value: 5 },\r\n        { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [\r\n        { name: '第1组', value: 65 },\r\n        { name: '第2组', value: 42 },\r\n        { name: '第3组', value: 63 },\r\n        { name: '第4组', value: 45 },\r\n        { name: '第5组', value: 68 },\r\n        { name: '第6组', value: 38 },\r\n        { name: '第7组', value: 41 },\r\n        { name: '第8组', value: 39 },\r\n        { name: '第9组', value: 43 },\r\n        { name: '第10组', value: 58 },\r\n        { name: '第11组', value: 36 },\r\n        { name: '第12组', value: 15 },\r\n        { name: '第13组', value: 55 },\r\n        { name: '第14组', value: 42 },\r\n        { name: '第15组', value: 66 },\r\n        { name: '第16组', value: 35 },\r\n        { name: '第17组', value: 28 },\r\n        { name: '第18组', value: 40 },\r\n        { name: '第19组', value: 48 }\r\n      ],\r\n      // 年龄数据\r\n      ageChartData: [\r\n        { name: '29岁以下', value: 20, percentage: '5%' },\r\n        { name: '30-39岁', value: 125, percentage: '30%' },\r\n        { name: '40-49岁', value: 168, percentage: '40%' },\r\n        { name: '50-59岁', value: 85, percentage: '20%' },\r\n        { name: '60岁以上', value: 20, percentage: '5%' }\r\n      ],\r\n      ageChartName: '年龄占比',\r\n      memberTotalNum: 418,\r\n      standingMemberTotalNum: 42\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 只有叶子节点（区县）才能选择\r\n      if (!data.children || data.children.length === 0) {\r\n        this.selectedArea = data.name\r\n        this.selectedDistrictCode = data.code\r\n        this.showAreaPopover = false\r\n        // 这里可以添加切换区县后的数据更新逻辑\r\n        console.log('选择了区县:', data)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-weight: 500;\r\n              font-size: 32px;\r\n            }\r\n\r\n            .count-img {\r\n              width: 118px;\r\n              margin-top: -10px;\r\n              margin-bottom: 10px;\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// el-popover 自定义样式\r\n.area-popover {\r\n  background: rgba(7, 52, 95, 0.95) !important;\r\n  border: 1px solid rgba(0, 181, 254, 0.5) !important;\r\n  border-radius: 8px !important;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\r\n\r\n  .area-tree-container {\r\n    .area-tree {\r\n      background: transparent;\r\n      color: #FFFFFF;\r\n\r\n      // 树节点样式\r\n      .el-tree-node {\r\n        .el-tree-node__content {\r\n          background: transparent;\r\n          color: #FFFFFF;\r\n          height: 36px;\r\n          line-height: 36px;\r\n          border-radius: 4px;\r\n          margin-bottom: 2px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 181, 254, 0.2);\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          .el-tree-node__expand-icon {\r\n            color: #1FC6FF;\r\n            font-size: 14px;\r\n          }\r\n\r\n          .el-tree-node__label {\r\n            color: inherit;\r\n            font-size: 14px;\r\n          }\r\n        }\r\n\r\n        // 当前选中节点\r\n        &.is-current>.el-tree-node__content {\r\n          background: rgba(0, 181, 254, 0.4);\r\n          color: #1FC6FF;\r\n          font-weight: bold;\r\n        }\r\n\r\n        // 父节点样式\r\n        &.is-expanded>.el-tree-node__content {\r\n          .el-tree-node__label {\r\n            font-weight: bold;\r\n            color: #1FC6FF;\r\n          }\r\n        }\r\n\r\n        // 子节点缩进\r\n        .el-tree-node__children {\r\n          .el-tree-node__content {\r\n            padding-left: 32px;\r\n\r\n            .el-tree-node__label {\r\n              font-size: 13px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 隐藏连接线\r\n      .el-tree-node__expand-icon.is-leaf {\r\n        color: transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}