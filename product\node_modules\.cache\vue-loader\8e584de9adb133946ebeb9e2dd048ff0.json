{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1755662087815}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfSBmcm9tICIuL2NvbW1pdHRlZVN0YXRpc3RpY3NCb3gudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTJjNDdjYWM5JnNjb3BlZD10cnVlJiIKaW1wb3J0IHNjcmlwdCBmcm9tICIuL2NvbW1pdHRlZVN0YXRpc3RpY3NCb3gudnVlP3Z1ZSZ0eXBlPXNjcmlwdCZsYW5nPWpzJiIKZXhwb3J0ICogZnJvbSAiLi9jb21taXR0ZWVTdGF0aXN0aWNzQm94LnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyYiCmltcG9ydCBzdHlsZTAgZnJvbSAiLi9jb21taXR0ZWVTdGF0aXN0aWNzQm94LnZ1ZT92dWUmdHlwZT1zdHlsZSZpbmRleD0wJmlkPTJjNDdjYWM5Jmxhbmc9c2NzcyZzY29wZWQ9dHJ1ZSYiCgoKLyogbm9ybWFsaXplIGNvbXBvbmVudCAqLwppbXBvcnQgbm9ybWFsaXplciBmcm9tICIhLi4vLi4vLi4vLi4vbm9kZV9tb2R1bGVzL3Z1ZS1sb2FkZXIvbGliL3J1bnRpbWUvY29tcG9uZW50Tm9ybWFsaXplci5qcyIKdmFyIGNvbXBvbmVudCA9IG5vcm1hbGl6ZXIoCiAgc2NyaXB0LAogIHJlbmRlciwKICBzdGF0aWNSZW5kZXJGbnMsCiAgZmFsc2UsCiAgbnVsbCwKICAiMmM0N2NhYzkiLAogIG51bGwKICAKKQoKLyogaG90IHJlbG9hZCAqLwppZiAobW9kdWxlLmhvdCkgewogIHZhciBhcGkgPSByZXF1aXJlKCJEOlxcenlcXHhtXFxwY1xccWR6eFxccHJvZHVjdFxcbm9kZV9tb2R1bGVzXFx2dWUtaG90LXJlbG9hZC1hcGlcXGRpc3RcXGluZGV4LmpzIikKICBhcGkuaW5zdGFsbChyZXF1aXJlKCd2dWUnKSkKICBpZiAoYXBpLmNvbXBhdGlibGUpIHsKICAgIG1vZHVsZS5ob3QuYWNjZXB0KCkKICAgIGlmICghYXBpLmlzUmVjb3JkZWQoJzJjNDdjYWM5JykpIHsKICAgICAgYXBpLmNyZWF0ZVJlY29yZCgnMmM0N2NhYzknLCBjb21wb25lbnQub3B0aW9ucykKICAgIH0gZWxzZSB7CiAgICAgIGFwaS5yZWxvYWQoJzJjNDdjYWM5JywgY29tcG9uZW50Lm9wdGlvbnMpCiAgICB9CiAgICBtb2R1bGUuaG90LmFjY2VwdCgiLi9jb21taXR0ZWVTdGF0aXN0aWNzQm94LnZ1ZT92dWUmdHlwZT10ZW1wbGF0ZSZpZD0yYzQ3Y2FjOSZzY29wZWQ9dHJ1ZSYiLCBmdW5jdGlvbiAoKSB7CiAgICAgIGFwaS5yZXJlbmRlcignMmM0N2NhYzknLCB7CiAgICAgICAgcmVuZGVyOiByZW5kZXIsCiAgICAgICAgc3RhdGljUmVuZGVyRm5zOiBzdGF0aWNSZW5kZXJGbnMKICAgICAgfSkKICAgIH0pCiAgfQp9CmNvbXBvbmVudC5vcHRpb25zLl9fZmlsZSA9ICJzcmMvdmlld3Mvc21hcnRCcmFpbkxhcmdlU2NyZWVuL2NvbW1pdHRlZVN0YXRpc3RpY3MvY29tbWl0dGVlU3RhdGlzdGljc0JveC52dWUiCmV4cG9ydCBkZWZhdWx0IGNvbXBvbmVudC5leHBvcnRz"}]}